/*
* =============================================================================
* Designer: jun@tian
* Description: nsy_000000
* Created: 2020/04/28 22:11:44
* ============================================================================= 
*/

create table if not exists system_config (
    id int primary key auto_increment,
    config_name varchar(30),
    config_value varchar(1000),
    create_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    create_by VARCHAR (45) COMMENT '创建者',
    update_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    update_by VARCHAR (45) COMMENT '更新者'
);


insert into system_config(config_name, config_value) values('sf_proxy_ips', 'local');
