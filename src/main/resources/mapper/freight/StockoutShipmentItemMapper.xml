<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nsy.api.tms.mapper.freight.StockoutShipmentItemMapper">
    <select id="countQtyByLogisticsNo" resultType="com.nsy.api.tms.domain.LogisticsInfoDto">
        SELECT
            sum( stockout_shipment_item.qty ) as totalQty,
            stockout_shipment.logistics_no as logisticsNo
        FROM stockout_shipment stockout_shipment
        LEFT JOIN stockout_shipment_item stockout_shipment_item on stockout_shipment.shipment_id = stockout_shipment_item.shipment_id
        where stockout_shipment.logistics_no in 
        <foreach collection="logisticsNos" item="logisticsNo" separator="," open="(" close=")">
            #{logisticsNo}
        </foreach>
          and stockout_shipment.is_deleted = 0
          and stockout_shipment_item.is_deleted = 0
          group by stockout_shipment.logistics_no
    </select>

</mapper>
