package com.nsy.api.tms.enumeration;

import com.nsy.api.core.apicore.util.StringUtils;

import java.util.Arrays;
/**
 * 产品说  渠道的计费方式 和 国家的计费方式不一样，所以拆开成两个枚举
 * <AUTHOR>
 * 2023-11-21
 */
public enum BillingTypeChannelEnum {
    INITIAL_ADDITIONAL_WEIGHT("续重"),
    UNIT_PRICE_WEIGHT("单价"),
    RANGE_WEIGHT("总价");

    String desc;

    BillingTypeChannelEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
    public static String getDescByName(String name) {
        BillingTypeChannelEnum valuationMethodEnum = Arrays.stream(BillingTypeChannelEnum.values())
                .filter((e) -> e.name().equals(name))
                .findFirst()
                .orElse(null);
        return valuationMethodEnum == null ? "" : valuationMethodEnum.getDesc();
    }

    public static String getNameByDesc(String desc) {
        if (!StringUtils.hasText(desc)) {
            return "";
        }
        BillingTypeChannelEnum valuationMethodEnum = Arrays.stream(BillingTypeChannelEnum.values())
                .filter((e) -> e.getDesc().equals(desc))
                .findFirst()
                .orElse(null);
        return valuationMethodEnum == null ? "" : valuationMethodEnum.name();
    }
}
