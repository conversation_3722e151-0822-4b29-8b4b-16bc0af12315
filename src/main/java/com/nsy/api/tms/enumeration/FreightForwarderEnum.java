package com.nsy.api.tms.enumeration;

/**
 * <AUTHOR>
 * @desc 货代枚举
 * @version 1.0.0
 */
public enum FreightForwarderEnum {
    AIR(8888, "空运"),
    OCEAN(8889, "海运"),
    LAND(8890, "陆运"),
    RAILWAY(8891, "铁运");


    private Integer id;
    private String value;

    FreightForwarderEnum(Integer id, String value) {
        this.id = id;
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public Integer getId() {
        return id;
    }
}
