package com.nsy.api.tms.enumeration;

import com.nsy.api.core.apicore.util.StringUtils;

import java.util.Arrays;

public enum ChannelFreightStatusEnum {
    INIT("待审核"),
    PASS("审核通过"),
    REJECT("驳回");

    String desc;

    ChannelFreightStatusEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
    public static String getDescByName(String name) {
        ChannelFreightStatusEnum valuationMethodEnum = Arrays.stream(ChannelFreightStatusEnum.values())
                .filter((e) -> e.name().equals(name))
                .findFirst()
                .orElse(null);
        return valuationMethodEnum == null ? "" : valuationMethodEnum.getDesc();
    }

    public static String getNameByDesc(String desc) {
        if (!StringUtils.hasText(desc)) {
            return "";
        }
        ChannelFreightStatusEnum valuationMethodEnum = Arrays.stream(ChannelFreightStatusEnum.values())
                .filter((e) -> e.getDesc().equals(desc))
                .findFirst()
                .orElse(null);
        return valuationMethodEnum == null ? "" : valuationMethodEnum.name();
    }
}
