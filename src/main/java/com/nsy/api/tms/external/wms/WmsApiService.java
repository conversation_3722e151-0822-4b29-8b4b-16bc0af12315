package com.nsy.api.tms.external.wms;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.tms.external.wms.request.SkuInfoListRequest;
import com.nsy.api.tms.external.wms.request.TmsNotifyPackageExceptionRequest;
import com.nsy.api.tms.external.wms.response.BdSpace;
import com.nsy.api.tms.external.wms.response.LogisticsDocumentsBaseRequestInfo;
import com.nsy.api.tms.external.wms.response.ProductSkuInfoMap;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;
import java.util.List;

@Service
public class WmsApiService {
    @Inject
    private RestTemplate restTemplate;
    @Value("${wms.api.service.url}")
    private String wmsServiceUrl;

    /**
     *  推送wms  查找审核人，发送订单包裹异常
     */
    public void notifyPackageException(TmsNotifyPackageExceptionRequest request) {
        String uri = String.format("%s/notify-package-exception", wmsServiceUrl);
        this.restTemplate.postForEntity(uri, request, Void.class);
    }

    /**
     * 通过仓库名称获取仓库信息
     *
     * @param spaceName 仓库名称
     * @return 仓库信息
     */
    public BdSpace getSpaceByName(String spaceName, String location) {
        String uri = String.format("%s/space/name/%s", wmsServiceUrl, spaceName);
        org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
        headers.set("location", location);
        org.springframework.http.HttpEntity<String> entity = new org.springframework.http.HttpEntity<>(headers);
        ResponseEntity<BdSpace> response = this.restTemplate.exchange(uri, org.springframework.http.HttpMethod.GET, entity, BdSpace.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            return response.getBody();
        } else {
            return null;
        }
    }

    public ProductSkuInfoMap productSkuInfoList(List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return new ProductSkuInfoMap();
        }
        SkuInfoListRequest request = new SkuInfoListRequest();
        request.setSkuList(skuList);
        String uri = String.format("%s/product-sku/info-list", wmsServiceUrl);
        ResponseEntity<ProductSkuInfoMap> response = this.restTemplate.postForEntity(uri, request, ProductSkuInfoMap.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            return response.getBody();
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

    public LogisticsDocumentsBaseRequestInfo getInvoiceItemSize(String logisticsNo, String location) {
        String uri = String.format("%s/logistics-documents/invoice-item/%s", wmsServiceUrl, logisticsNo);
        org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
        headers.set("location", location);
        org.springframework.http.HttpEntity<String> entity = new org.springframework.http.HttpEntity<>(headers);
        ResponseEntity<LogisticsDocumentsBaseRequestInfo> response = this.restTemplate.exchange(uri, org.springframework.http.HttpMethod.GET, entity, LogisticsDocumentsBaseRequestInfo.class);
        if (HttpStatus.OK.value() == response.getStatusCode().value()) {
            return response.getBody();
        } else {
            throw new BusinessServiceException("请求失败");
        }
    }

}
