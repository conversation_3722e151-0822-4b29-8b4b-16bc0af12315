package com.nsy.api.tms.dao.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Entity
@Table(name = "tms_tag")
public class TmsTagEntity extends BaseDataManipulationEntity {

    /*** 物流公司*/
    @Column(name = "logistics_method")
    private String logisticsMethod;

    /*** 物流公司*/
    @Column(name = "logistics_company")
    private String logisticsCompany;

    /*** 物流类型*/
    @Column(name = "logistics_type")
    private String logisticsType;

    /*** 物流渠道编码*/
    @Column(name = "logistics_channel_code")
    private String logisticsChannelCode;

    /*** 包裹编码*/
    @Column(name = "logistics_no")
    private String logisticsNo;

    /*** 商通订单号*/
    @Column(name = "tid")
    private String tid;

    /*** 业务单号*/
    @Column(name = "business_key")
    private String businessKey;

    /*** 标签*/
    @Column(name = "tag")
    private String tag;


    /*** 备注 */
    @Column(name = "remarks")
    private String remarks;

    public String getLogisticsType() {
        return logisticsType;
    }

    public void setLogisticsType(String logisticsType) {
        this.logisticsType = logisticsType;
    }

    public String getLogisticsMethod() {
        return logisticsMethod;
    }

    public void setLogisticsMethod(String logisticsMethod) {
        this.logisticsMethod = logisticsMethod;
    }

    public String getLogisticsChannelCode() {
        return logisticsChannelCode;
    }

    public void setLogisticsChannelCode(String logisticsChannelCode) {
        this.logisticsChannelCode = logisticsChannelCode;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getBusinessKey() {
        return businessKey;
    }

    public void setBusinessKey(String businessKey) {
        this.businessKey = businessKey;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
}
