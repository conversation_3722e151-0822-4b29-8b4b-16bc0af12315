package com.nsy.api.tms.dao.entity.freight;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.api.tms.dao.entity.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 渠道邮编国家分区映射(BdChannelCountryAreaMapping)实体类
 *
 * <AUTHOR>
 * @since 2024-04-11 16:56:13
 */
@Entity
@Table(name = "bd_channel_country_area_mapping")
@TableName("bd_channel_country_area_mapping")
public class BdChannelCountryAreaMappingEntity extends BaseMpEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 物流渠道编码
     */
    private String channelCode;

    /**
     * 国家code
     */
    private String countryCode;

    /**
     * 邮编
     */
    private String postCode;

    /**
     * 分区名称
     */
    private String areaName;


    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

}

