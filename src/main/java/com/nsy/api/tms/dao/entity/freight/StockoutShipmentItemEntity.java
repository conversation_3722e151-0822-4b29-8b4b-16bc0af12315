package com.nsy.api.tms.dao.entity.freight;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nsy.api.tms.dao.entity.BaseMpEntity;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 装箱清单明细
 *
 * <AUTHOR>
 * @since 1.0
 */
@Entity
@Table(name = "stockout_shipment_item")
@TableName("stockout_shipment_item")
public class StockoutShipmentItemEntity extends BaseMpEntity {
    /**
     * 主键id
     */
    @Id
    @TableId(type = IdType.NONE)
    private Integer shipmentItemId;

    /**
     * 地区
     */
    private String location;

    /**
     * 装箱id
     */
    private Integer shipmentId;

    /**
     * 出库单号
     */
    private String stockoutOrderNo;

    /**
     * 波次id
     */
    private Integer batchId;

    /**
     * 出库单明细id
     */
    private Integer stockoutOrderItemId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单明细id
     */
    private String orderItemId;

    /**
     * 规格编码Id
     */
    private Integer specId;

    /**
     * 规格编码
     */
    private String sku;

    /**
     * 数量
     */
    private Integer qty;

    /**
     * 是否删除：1是，0否
     */
    private Integer isDeleted;

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getShipmentItemId() {
        return shipmentItemId;
    }

    public void setShipmentItemId(Integer shipmentItemId) {
        this.shipmentItemId = shipmentItemId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getShipmentId() {
        return shipmentId;
    }

    public void setShipmentId(Integer shipmentId) {
        this.shipmentId = shipmentId;
    }


    public String getStockoutOrderNo() {
        return stockoutOrderNo;
    }

    public void setStockoutOrderNo(String stockoutOrderNo) {
        this.stockoutOrderNo = stockoutOrderNo;
    }

    public Integer getStockoutOrderItemId() {
        return stockoutOrderItemId;
    }

    public void setStockoutOrderItemId(Integer stockoutOrderItemId) {
        this.stockoutOrderItemId = stockoutOrderItemId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(String orderItemId) {
        this.orderItemId = orderItemId;
    }

    public Integer getSpecId() {
        return specId;
    }

    public void setSpecId(Integer specId) {
        this.specId = specId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getQty() {
        return qty;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }

    public Integer getBatchId() {
        return batchId;
    }

    public void setBatchId(Integer batchId) {
        this.batchId = batchId;
    }
}
