package com.nsy.api.tms.domain;

import java.util.Date;

/**
 * 异常任务返回实体
 */
public class TmsAlertTaskReturnInfo {

    private Integer id;

    private String logisticsNo;

    private String tid;

    private String logisticsMethod;

    private String logisticsCompany;

    private String deptName;

    /*** 店铺id*/
    private Integer storeId;

    /*** 物流类型*/
    private String logisticsType;

    /*** 物流渠道编码*/
    private String logisticsChannelCode;

    /*** 状态*/
    private String packageStatus;

    private Date shipDate;

    /*** 物流流转最后更新时间*/
    private Date routeLastUpdateTime;

    /*** 触发条件*/
    private String triggerCondition;

    /*** 处理状态:漏发，已揽收，忽略/配件中，已送达，物流丢件，仓库丢件,已换单号，客户取消订单, 投递失败,协商索赔，调整发货量，停止合作，调整超期参数，其他',*/
    private String processStatus;

    /*** 任务状态: 0---待处理，1---已处理*/

    private Integer taskStatus;

    /*任务状态: 0---不索赔，1---索赔*/
    private Integer isClaim;

    /*是否退件: 0---不退件，1---退件*/
    //private Integer isReturn;

    /*** 备注*/
    private String remarks;

    //物流渠道名称
    private String logisticsChannelName;

    //官网追踪链接
    private String officialWebsite;

    private Date createDate;

    //预计完成时间
    private Date estimatedFinishTime;

    //店铺名称
    private String storeName;

    //目的国
    private String countryName;

    //转运单号
    private String secondaryNumber;

    public String getSecondaryNumber() {
        return secondaryNumber;
    }

    public void setSecondaryNumber(String secondaryNumber) {
        this.secondaryNumber = secondaryNumber;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public Date getEstimatedFinishTime() {
        return estimatedFinishTime;
    }

    public void setEstimatedFinishTime(Date estimatedFinishTime) {
        this.estimatedFinishTime = estimatedFinishTime;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getLogisticsMethod() {
        return logisticsMethod;
    }

    public void setLogisticsMethod(String logisticsMethod) {
        this.logisticsMethod = logisticsMethod;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getLogisticsType() {
        return logisticsType;
    }

    public void setLogisticsType(String logisticsType) {
        this.logisticsType = logisticsType;
    }

    public String getLogisticsChannelCode() {
        return logisticsChannelCode;
    }

    public void setLogisticsChannelCode(String logisticsChannelCode) {
        this.logisticsChannelCode = logisticsChannelCode;
    }

    public String getPackageStatus() {
        return packageStatus;
    }

    public void setPackageStatus(String packageStatus) {
        this.packageStatus = packageStatus;
    }

    public Date getShipDate() {
        return shipDate;
    }

    public void setShipDate(Date shipDate) {
        this.shipDate = shipDate;
    }

    public Date getRouteLastUpdateTime() {
        return routeLastUpdateTime;
    }

    public void setRouteLastUpdateTime(Date routeLastUpdateTime) {
        this.routeLastUpdateTime = routeLastUpdateTime;
    }

    public String getTriggerCondition() {
        return triggerCondition;
    }

    public void setTriggerCondition(String triggerCondition) {
        this.triggerCondition = triggerCondition;
    }

    public String getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(String processStatus) {
        this.processStatus = processStatus;
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    public Integer getIsClaim() {
        return isClaim;
    }

    public void setIsClaim(Integer isClaim) {
        this.isClaim = isClaim;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getLogisticsChannelName() {
        return logisticsChannelName;
    }

    public void setLogisticsChannelName(String logisticsChannelName) {
        this.logisticsChannelName = logisticsChannelName;
    }

    public String getOfficialWebsite() {
        return officialWebsite;
    }

    public void setOfficialWebsite(String officialWebsite) {
        this.officialWebsite = officialWebsite;
    }
}
