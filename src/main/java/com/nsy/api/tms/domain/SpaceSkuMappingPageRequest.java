package com.nsy.api.tms.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nsy.api.tms.request.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-07-09 18:18:44
 */
@ApiModel(value = "SpaceSkuMappingPageRequest", description = "首页列表req")
public class SpaceSkuMappingPageRequest extends PageRequest {
    @ApiModelProperty("location")
    private String location;

    /**
     * 仓库id
     */
    @ApiModelProperty("仓库id")
    private List<Integer> spaceIdList;

    /**
     * 仓库平台
     */
    @ApiModelProperty("仓库平台")
    private String platform;

    /**
     * 系统sku
     */
    @ApiModelProperty("系统sku")
    private String sku;

    /**
     * 平台仓库sku
     */
    @ApiModelProperty("平台仓库sku")
    private String spaceSku;

    /**
     * 平台仓库条码
     */
    @ApiModelProperty("平台仓库条码")
    private String spaceBarcode;

    @ApiModelProperty("创建人")
    private String createBy;

    @ApiModelProperty("创建开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDateBegin;

    @ApiModelProperty("创建结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDateEnd;

    public Date getCreateDateBegin() {
        return createDateBegin;
    }

    public void setCreateDateBegin(Date createDateBegin) {
        this.createDateBegin = createDateBegin;
    }

    public Date getCreateDateEnd() {
        return createDateEnd;
    }

    public void setCreateDateEnd(Date createDateEnd) {
        this.createDateEnd = createDateEnd;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public List<Integer> getSpaceIdList() {
        return spaceIdList;
    }

    public void setSpaceIdList(List<Integer> spaceIdList) {
        this.spaceIdList = spaceIdList;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public String getSpaceSku() {
        return spaceSku;
    }

    public void setSpaceSku(String spaceSku) {
        this.spaceSku = spaceSku;
    }

    public String getSpaceBarcode() {
        return spaceBarcode;
    }

    public void setSpaceBarcode(String spaceBarcode) {
        this.spaceBarcode = spaceBarcode;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
}

