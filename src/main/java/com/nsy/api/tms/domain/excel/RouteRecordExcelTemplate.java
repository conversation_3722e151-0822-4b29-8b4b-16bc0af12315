package com.nsy.api.tms.domain.excel;

import com.nsy.api.tms.utils.poi.excel.Cell;
import java.util.Date;

public class RouteRecordExcelTemplate {

    @Cell(index = 0, title = "logistics_no")
    private String logisticsNo;
    @Cell(index = 1, title = "secondary_number")
    private String secondaryNumber;
    @Cell(index = 2, title = "status")
    private String status;
    @Cell(index = 3, title = "address")
    private String address;
    @Cell(index = 4, title = "label_url")
    private String labelUrl;
    @Cell(index = 5, title = "tid")
    private String tid;
    @Cell(index = 6, title = "ship_date")
    private Date shipDate;
    @Cell(index = 7, title = "logistics_method")
    private String logisticsMethod;
    @Cell(index = 8, title = "logistics_channel_code")
    private String logisticsChannelCode;

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getLabelUrl() {
        return labelUrl;
    }

    public void setLabelUrl(String labelUrl) {
        this.labelUrl = labelUrl;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public Date getShipDate() {
        return shipDate;
    }

    public void setShipDate(Date shipDate) {
        this.shipDate = shipDate;
    }


    public String getLogisticsMethod() {
        return logisticsMethod;
    }

    public void setLogisticsMethod(String logisticsMethod) {
        this.logisticsMethod = logisticsMethod;
    }

    public String getLogisticsChannelCode() {
        return logisticsChannelCode;
    }

    public void setLogisticsChannelCode(String logisticsChannelCode) {
        this.logisticsChannelCode = logisticsChannelCode;
    }

    public String getSecondaryNumber() {
        return secondaryNumber;
    }

    public void setSecondaryNumber(String secondaryNumber) {
        this.secondaryNumber = secondaryNumber;
    }
}
