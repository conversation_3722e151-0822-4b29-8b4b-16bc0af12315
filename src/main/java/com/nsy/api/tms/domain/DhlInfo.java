package com.nsy.api.tms.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;

@ApiModel(description = "DHL物流额外信息")
public class DhlInfo {

    @ApiModelProperty(value = "发票形式，DHL如需要商业发票需必填 PFI:形式发票，CMI：商业发票")
    private String invoiceType;

    @ApiModelProperty(value = "账户号码")
    @NotEmpty
    private String shipperAccountNumber;

    @ApiModelProperty(value = "第三方付款账号")
    private String billingAccountNumber;

    @ApiModelProperty(value = "DHL产品代码 P:包裹,D:文件,N:本地件")
    private String productCode;

    @ApiModelProperty(value = "包裹类型(EE:DHL Express Envelope, OD:Other DHL Packaging, CP:Customer-provided, JB-Jumbo box, JJ-Junior jumbo Box, DF-DHL Flyer, YP-Your )")
    @NotEmpty
    private String packageType;

    @ApiModelProperty(value = "主要品名")
    @NotEmpty
    private String contents;

    @ApiModelProperty(value = "包裹件数,DHL系统并不效验, 以实际包裹件数为准,整数")
    @NotEmpty
    private Integer numberOfPieces;

    @ApiModelProperty(value = "使用DHL系统创建发票")
    private String useDHLInvoice;

    @ApiModelProperty(value = "手续费")
    private Double handingFee;

    @ApiModelProperty(value = "运费")
    private Double freightCost;

    /**
     * 币种编码:如：美金 USD,欧元 EUR,墨西哥币 MXN
     */
    private String currencyCode;
    @ApiModelProperty(value = "贸易条款")
    private String tradeTerms;

    public String getTradeTerms() {
        return tradeTerms;
    }

    public void setTradeTerms(String tradeTerms) {
        this.tradeTerms = tradeTerms;
    }

    public Double getHandingFee() {
        return handingFee;
    }

    public void setHandingFee(Double handingFee) {
        this.handingFee = handingFee;
    }

    public Double getFreightCost() {
        return freightCost;
    }

    public void setFreightCost(Double freightCost) {
        this.freightCost = freightCost;
    }

    public String getUseDHLInvoice() {
        return useDHLInvoice;
    }

    public void setUseDHLInvoice(String useDHLInvoice) {
        this.useDHLInvoice = useDHLInvoice;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getPackageType() {
        return packageType;
    }

    public void setPackageType(String packageType) {
        this.packageType = packageType;
    }

    public String getContents() {
        return contents;
    }

    public void setContents(String contents) {
        this.contents = contents;
    }

    public Integer getNumberOfPieces() {
        return numberOfPieces;
    }

    public void setNumberOfPieces(Integer numberOfPieces) {
        this.numberOfPieces = numberOfPieces;
    }

    public String getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType;
    }

    public String getShipperAccountNumber() {
        return shipperAccountNumber;
    }

    public void setShipperAccountNumber(String shipperAccountNumber) {
        this.shipperAccountNumber = shipperAccountNumber;
    }

    public String getBillingAccountNumber() {
        return billingAccountNumber;
    }

    public void setBillingAccountNumber(String billingAccountNumber) {
        this.billingAccountNumber = billingAccountNumber;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }
}
