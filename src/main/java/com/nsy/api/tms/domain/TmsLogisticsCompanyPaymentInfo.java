package com.nsy.api.tms.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @date 2023/8/28 17:42
 */
@ApiModel(description = "物流公司日志信息")
public class TmsLogisticsCompanyPaymentInfo {

    @ApiModelProperty(value = "物流公司id", name = "logisticsId")
    private Integer logisticsId;


    /**
     * 账号类型
     */
    @ApiModelProperty(value = "账号类型", name = "accountType")
    private String accountType;

    /**
     * 开户行
     */
    @ApiModelProperty(value = "开户行", name = "bankName")
    private String bankName;

    /**
     * 银行开户名
     */
    @ApiModelProperty(value = "银行开户名", name = "bankAccountName")
    private String bankAccountName;

    /**
     * 开户账号
     */
    @ApiModelProperty(value = "开户账号", name = "bankAccount")
    private String bankAccount;

    /**
     * 持有人
     */
    @ApiModelProperty(value = "持有人", name = "holder")
    private String holder;

    public Integer getLogisticsId() {
        return logisticsId;
    }

    public void setLogisticsId(Integer logisticsId) {
        this.logisticsId = logisticsId;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankAccountName() {
        return bankAccountName;
    }

    public void setBankAccountName(String bankAccountName) {
        this.bankAccountName = bankAccountName;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getHolder() {
        return holder;
    }

    public void setHolder(String holder) {
        this.holder = holder;
    }
}
