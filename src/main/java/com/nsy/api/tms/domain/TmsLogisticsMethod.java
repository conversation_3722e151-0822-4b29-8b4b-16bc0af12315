package com.nsy.api.tms.domain;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 1.0
 */
public class TmsLogisticsMethod {

    /**  */
    private Integer id;

    /** 物流方式 */
    private String logisticsMethod;

    /** 官方网站 */
    private String officialWebsite;

    /** 获取单号地址 */
    private String orderUrl;

    /** 获取面单地址 */
    private String labelUrl;

    /** 包裹追踪地址 */
    private String truckUrl;

    /** 获取发票地址 */
    private String invoiceUrl;

    /** 服务地址 */
    private String serviceUrl;

    /** ip地址 */
    private String hostUrl;

    /** wsdl地址 */
    private String wsdlUrl;

    /** 认证地址 */
    private String authenticationUrl;

    /** 版本号 */
    private Integer version;

    /** 创建时间 */
    private Date createDate;

    /** 创建者 */
    private String createBy;

    /** 更新时间 */
    private Date updateDate;

    /** 更新者 */
    private String updateBy;


    public Integer getId() {
        return this.id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getLogisticsMethod() {
        return this.logisticsMethod;
    }

    public void setLogisticsMethod(String logisticsMethod) {
        this.logisticsMethod = logisticsMethod;
    }
    public String getOfficialWebsite() {
        return this.officialWebsite;
    }

    public void setOfficialWebsite(String officialWebsite) {
        this.officialWebsite = officialWebsite;
    }
    public String getOrderUrl() {
        return this.orderUrl;
    }

    public void setOrderUrl(String orderUrl) {
        this.orderUrl = orderUrl;
    }
    public String getLabelUrl() {
        return this.labelUrl;
    }

    public void setLabelUrl(String labelUrl) {
        this.labelUrl = labelUrl;
    }
    public String getTruckUrl() {
        return this.truckUrl;
    }

    public void setTruckUrl(String truckUrl) {
        this.truckUrl = truckUrl;
    }
    public String getInvoiceUrl() {
        return this.invoiceUrl;
    }

    public void setInvoiceUrl(String invoiceUrl) {
        this.invoiceUrl = invoiceUrl;
    }
    public String getServiceUrl() {
        return this.serviceUrl;
    }

    public void setServiceUrl(String serviceUrl) {
        this.serviceUrl = serviceUrl;
    }
    public String getHostUrl() {
        return this.hostUrl;
    }

    public void setHostUrl(String hostUrl) {
        this.hostUrl = hostUrl;
    }
    public String getWsdlUrl() {
        return this.wsdlUrl;
    }

    public void setWsdlUrl(String wsdlUrl) {
        this.wsdlUrl = wsdlUrl;
    }
    public String getAuthenticationUrl() {
        return this.authenticationUrl;
    }

    public void setAuthenticationUrl(String authenticationUrl) {
        this.authenticationUrl = authenticationUrl;
    }
    public Integer getVersion() {
        return this.version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
    public String getCreateBy() {
        return this.createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public Date getUpdateDate() {
        return this.updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
    public String getUpdateBy() {
        return this.updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
}