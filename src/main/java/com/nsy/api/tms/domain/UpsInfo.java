package com.nsy.api.tms.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "UPS额外信息")

public class UpsInfo {
    @ApiModelProperty(value = "发票编号")
    private String invoiceNumber;
    @ApiModelProperty(value = "账号")
    private String accountNumber;
    @ApiModelProperty(value = "服务类型 65:UPS速快 08:UPS快捷")
    private String serviceType;
    @ApiModelProperty(value = "总价")
    private String totalMoney;
    @ApiModelProperty(value = "总件数")
    private String totalQty;
    @ApiModelProperty(value = "包裹描述")
    private String packageDescription;
    @ApiModelProperty(value = "包裹数")
    private int packageCount;
    @ApiModelProperty(value = "运费")
    private String freightCharges;
    @ApiModelProperty(value = "手续费")
    private String handingFee;
    @ApiModelProperty(value = "使用UPS无纸化发票,true ups发票,false 自己的发票")
    private Boolean useUpsInvoice;

    public Boolean getUseUpsInvoice() {
        return useUpsInvoice;
    }

    public void setUseUpsInvoice(Boolean useUpsInvoice) {
        this.useUpsInvoice = useUpsInvoice;
    }

    public String getHandingFee() {
        return handingFee;
    }

    public void setHandingFee(String handingFee) {
        this.handingFee = handingFee;
    }

    public String getFreightCharges() {
        return freightCharges;
    }

    public void setFreightCharges(String freightCharges) {
        this.freightCharges = freightCharges;
    }

    public int getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(int packageCount) {
        this.packageCount = packageCount;
    }

    public String getPackageDescription() {
        return packageDescription;
    }

    public void setPackageDescription(String packageDescription) {
        this.packageDescription = packageDescription;
    }

    public String getTotalMoney() {
        return totalMoney;
    }

    public void setTotalMoney(String totalMoney) {
        this.totalMoney = totalMoney;
    }

    public String getTotalQty() {
        return totalQty;
    }

    public void setTotalQty(String totalQty) {
        this.totalQty = totalQty;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }
}
