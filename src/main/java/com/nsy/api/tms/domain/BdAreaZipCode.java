package com.nsy.api.tms.domain;

import java.util.Date;
/**
 * <AUTHOR>
 * @since 1.0
 */
public class BdAreaZipCode {

    /**  */
    private Integer id;

    /** 父级邮编 */
    private String parentZipCode;

    /** 国家/地区代码 */
    private String countryTerritoryCode;

    /** 邮编 */
    private String zipCode;

    /** 地区名称 */
    private String areaName;

    /**  */
    private String areaNameCn;

    /**  */
    private String areaNameEn;

    /** 版本号 */
    private Integer version;

    /** 创建时间 */
    private Date createDate;

    /** 创建者 */
    private String createBy;

    /** 更新时间 */
    private Date updateDate;

    /** 更新者 */
    private String updateBy;


    public Integer getId() {
        return this.id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCountryTerritoryCode() {
        return this.countryTerritoryCode;
    }

    public void setCountryTerritoryCode(String countryTerritoryCode) {
        this.countryTerritoryCode = countryTerritoryCode;
    }

    public String getParentZipCode() {
        return parentZipCode;
    }

    public void setParentZipCode(String parentZipCode) {
        this.parentZipCode = parentZipCode;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public String getAreaName() {
        return this.areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }
    public String getAreaNameCn() {
        return this.areaNameCn;
    }

    public void setAreaNameCn(String areaNameCn) {
        this.areaNameCn = areaNameCn;
    }
    public String getAreaNameEn() {
        return this.areaNameEn;
    }

    public void setAreaNameEn(String areaNameEn) {
        this.areaNameEn = areaNameEn;
    }
    public Integer getVersion() {
        return this.version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }
    public Date getCreateDate() {
        return this.createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
    public String getCreateBy() {
        return this.createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public Date getUpdateDate() {
        return this.updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
    public String getUpdateBy() {
        return this.updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
}