package com.nsy.api.tms.domain;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: HUANG Tao
 * @date: 2019/12/30
 */
public class TmsExpiredTask {

    private Integer id;

    private String logisticsMethod;

    private String logisticsCompany;

    private String logisticsType;

    /*** 任务状态: 0---待处理，1---已处理*/
    private int taskStatus;

    /*** 状态*/
    private String processStatus;

    /*超期率阀值*/
    private String threshold;

    /*** 统计时长*/
    private Integer statisticDuration;

    /*统计开始时间*/
    private Date statisticStartDate;

    /*统计结束时间*/
    private Date statisticEndDate;

    /*** 包裹数*/
    private int packageCount;

    /*** 超期包裹数*/
    private int expiredPackageCount;

    /*** 超期率*/
    private String expiredRate;

    /*任务生成时间*/
    private Date createDate;

    /*索赔金额*/
    private BigDecimal claimAmount = BigDecimal.ZERO;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getLogisticsMethod() {
        return logisticsMethod;
    }

    public void setLogisticsMethod(String logisticsMethod) {
        this.logisticsMethod = logisticsMethod;
    }

    public String getLogisticsType() {
        return logisticsType;
    }

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public void setLogisticsType(String logisticsType) {
        this.logisticsType = logisticsType;
    }

    public int getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(int taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(String processStatus) {
        this.processStatus = processStatus;
    }

    public String getThreshold() {
        return threshold;
    }

    public void setThreshold(String threshold) {
        this.threshold = threshold;
    }

    public Integer getStatisticDuration() {
        return statisticDuration;
    }

    public void setStatisticDuration(Integer statisticDuration) {
        this.statisticDuration = statisticDuration;
    }

    public Date getStatisticStartDate() {
        return statisticStartDate;
    }

    public void setStatisticStartDate(Date statisticStartDate) {
        this.statisticStartDate = statisticStartDate;
    }

    public Date getStatisticEndDate() {
        return statisticEndDate;
    }

    public void setStatisticEndDate(Date statisticEndDate) {
        this.statisticEndDate = statisticEndDate;
    }

    public int getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(int packageCount) {
        this.packageCount = packageCount;
    }

    public int getExpiredPackageCount() {
        return expiredPackageCount;
    }

    public void setExpiredPackageCount(int expiredPackageCount) {
        this.expiredPackageCount = expiredPackageCount;
    }

    public String getExpiredRate() {
        return expiredRate;
    }

    public void setExpiredRate(String expiredRate) {
        this.expiredRate = expiredRate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public BigDecimal getClaimAmount() {
        return claimAmount;
    }

    public void setClaimAmount(BigDecimal claimAmount) {
        this.claimAmount = claimAmount;
    }
}
