package com.nsy.api.tms.domain;

public class TmsTaskCollectInfo {
    private String logisticsType;
    //超期件数
    private Integer overDueCount;
    //异常件数
    private Integer abnormalCount;
    //丢件件数
    private Integer lostCount;
    //未收件数
    private Integer notReceiveCount;
    //索赔件数
    private Integer calimPendingCount;
    //退回件数
    private Integer returnPendingCount;

    //异常件数 即将超期任务数
    private Long abnormalOverDateCount;
    //丢件件数 即将超期任务数
    private Long lostOverDateCount;
    //未收件数 即将超期任务数
    private Long notReceiveOverDateCount;

    public String getLogisticsType() {
        return logisticsType;
    }

    public void setLogisticsType(String logisticsType) {
        this.logisticsType = logisticsType;
    }

    public Integer getOverDueCount() {
        return overDueCount;
    }

    public void setOverDueCount(Integer overDueCount) {
        this.overDueCount = overDueCount;
    }

    public Integer getAbnormalCount() {
        return abnormalCount;
    }

    public void setAbnormalCount(Integer abnormalCount) {
        this.abnormalCount = abnormalCount;
    }

    public Integer getLostCount() {
        return lostCount;
    }

    public void setLostCount(Integer lostCount) {
        this.lostCount = lostCount;
    }

    public Integer getNotReceiveCount() {
        return notReceiveCount;
    }

    public void setNotReceiveCount(Integer notReceiveCount) {
        this.notReceiveCount = notReceiveCount;
    }

    public Integer getCalimPendingCount() {
        return calimPendingCount;
    }

    public void setCalimPendingCount(Integer calimPendingCount) {
        this.calimPendingCount = calimPendingCount;
    }

    public Integer getReturnPendingCount() {
        return returnPendingCount;
    }

    public void setReturnPendingCount(Integer returnPendingCount) {
        this.returnPendingCount = returnPendingCount;
    }

    public Long getAbnormalOverDateCount() {
        return abnormalOverDateCount;
    }

    public void setAbnormalOverDateCount(Long abnormalOverDateCount) {
        this.abnormalOverDateCount = abnormalOverDateCount;
    }

    public Long getLostOverDateCount() {
        return lostOverDateCount;
    }

    public void setLostOverDateCount(Long lostOverDateCount) {
        this.lostOverDateCount = lostOverDateCount;
    }

    public Long getNotReceiveOverDateCount() {
        return notReceiveOverDateCount;
    }

    public void setNotReceiveOverDateCount(Long notReceiveOverDateCount) {
        this.notReceiveOverDateCount = notReceiveOverDateCount;
    }
}
