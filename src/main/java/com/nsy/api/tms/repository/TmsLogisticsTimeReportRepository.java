package com.nsy.api.tms.repository;

import com.nsy.api.tms.dao.entity.TmsLogisticsTimeReportEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * @author: HUANG Tao
 * @date: 2020/2/16
 */
public interface TmsLogisticsTimeReportRepository extends JpaRepository<TmsLogisticsTimeReportEntity, Integer>, JpaSpecificationExecutor<TmsLogisticsTimeReportEntity> {
    /**
     * 根据jobRunDate删除，防止重复记录
     * @param jobRunDate
     * @return
     */
    int deleteByJobRunDate(String jobRunDate);
}
