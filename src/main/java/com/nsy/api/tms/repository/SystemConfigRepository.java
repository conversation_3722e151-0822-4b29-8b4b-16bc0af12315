package com.nsy.api.tms.repository;

import com.nsy.api.tms.dao.entity.SystemConfigEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface SystemConfigRepository extends JpaRepository<SystemConfigEntity, Integer>, JpaSpecificationExecutor<SystemConfigEntity> {
    SystemConfigEntity findFirstByConfigName(String configName);
}
