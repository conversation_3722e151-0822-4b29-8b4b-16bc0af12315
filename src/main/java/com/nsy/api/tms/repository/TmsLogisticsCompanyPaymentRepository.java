package com.nsy.api.tms.repository;

import com.nsy.api.tms.dao.entity.TmsLogisticsCompanyPaymentEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/29 14:18
 */
public interface TmsLogisticsCompanyPaymentRepository extends JpaRepository<TmsLogisticsCompanyPaymentEntity, Integer>, JpaSpecificationExecutor<TmsLogisticsCompanyPaymentEntity> {

    /**
     * 查询物流公司的付款公司信息
     *
     * @param logisticsId
     * @return
     */
    List<TmsLogisticsCompanyPaymentEntity> findByLogisticsId(Integer logisticsId);

    /**
     * 删除物流公司下的所有账号信息
     *
     * @param logisticsId
     */
    void deleteByLogisticsId(Integer logisticsId);
}
