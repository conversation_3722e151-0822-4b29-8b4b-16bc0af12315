package com.nsy.api.tms.repository;

import com.nsy.api.tms.dao.entity.TmsTimelinessConfigEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public interface TmsTimelinessConfigRepository extends JpaRepository<TmsTimelinessConfigEntity, Integer>, JpaSpecificationExecutor<TmsTimelinessConfigEntity> {
    TmsTimelinessConfigEntity findByLogisticsMethodAndCountryCodeAndType(String logisticsMethod, String countryCode, String configType);

    List<TmsTimelinessConfigEntity> findByLogisticsMethodAndType(String logisticsMethod, String configType);
}
