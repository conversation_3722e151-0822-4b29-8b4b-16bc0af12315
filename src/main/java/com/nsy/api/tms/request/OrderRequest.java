package com.nsy.api.tms.request;

import com.nsy.api.tms.domain.OrderInfo;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public class OrderRequest {
    /***是否异步*/
    Integer async = 0;

    /***是否重新获取*/
    Integer reOrder = 1;

    OrderInfo orderInfo;


    public Integer getAsync() {
        return async;
    }

    public void setAsync(Integer async) {
        this.async = async;
    }

    public Integer getReOrder() {
        return reOrder;
    }

    public void setReOrder(Integer reOrder) {
        this.reOrder = reOrder;
    }

    @Valid
    public OrderInfo getOrderInfo() {
        return orderInfo;
    }

    public void setOrderInfo(OrderInfo orderInfo) {
        this.orderInfo = orderInfo;
    }

}
