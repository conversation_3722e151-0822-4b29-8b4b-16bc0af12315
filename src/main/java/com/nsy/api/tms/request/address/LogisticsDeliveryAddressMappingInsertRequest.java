package com.nsy.api.tms.request.address;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024-03-11 14:11:36
 */
@ApiModel(value = "LogisticsDeliveryAddressMappingInsertRequest", description = "新增request")
public class LogisticsDeliveryAddressMappingInsertRequest {

    @ApiModelProperty("location")
    @NotBlank
    private String location;
    /**
     * 地址id
     */
    @ApiModelProperty("地址id")
    @NotNull
    private Integer addressId;

    /**
     * 物流公司id
     */
    @ApiModelProperty("物流公司id")
    private Integer companyId;

    /**
     * 物流渠道id
     */
    @ApiModelProperty("物流渠道id")
    private Integer channelId;

    /**
     * 店铺id
     */
    @ApiModelProperty("店铺id")
    private Integer storeId;


    /**
     * 店铺名
     */
    @ApiModelProperty("店铺名")
    private String storeName;

    @ApiModelProperty("是否默认地址")
    private Integer defaultShippingAddress = 0;

    public Integer getDefaultShippingAddress() {
        return defaultShippingAddress;
    }

    public void setDefaultShippingAddress(Integer defaultShippingAddress) {
        this.defaultShippingAddress = defaultShippingAddress;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getAddressId() {
        return addressId;
    }

    public void setAddressId(Integer addressId) {
        this.addressId = addressId;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public Integer getChannelId() {
        return channelId;
    }

    public void setChannelId(Integer channelId) {
        this.channelId = channelId;
    }

    public Integer getStoreId() {
        return storeId;
    }

    public void setStoreId(Integer storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }
}

