package com.nsy.api.tms.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2024-03-11 14:11:36
 */
@ApiModel(value = "OrderTrackingRequest", description = "订单追踪请求")
public class OrderTrackingRequest {

    @ApiModelProperty("订单号")
    @NotBlank(message = "orderNo不能为空")
    private String orderNo;

    @ApiModelProperty("订单类型：1-FBM, 2-FBA")
    @NotNull(message = "orderType不能为空")
    private Integer orderType;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }
}

