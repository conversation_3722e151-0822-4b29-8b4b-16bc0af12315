package com.nsy.api.tms.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@ApiModel(value = "TmsLogisticsAccountConfigAddRequest", description = "物流方式配置新增request")
public class TmsLogisticsAccountConfigAddRequest {

    @ApiModelProperty(value = "物流方式id", name = "logisticsAccountId")
    private Integer logisticsAccountId;
    @NotEmpty
    @ApiModelProperty(value = "地区(仓库系统当前地区)", name = "location")
    private String location;

    @ApiModelProperty(value = "配置明细", name = "accountConfigList")
    private List<AccountConfig> accountConfigList;

    public Integer getLogisticsAccountId() {
        return logisticsAccountId;
    }

    public void setLogisticsAccountId(Integer logisticsAccountId) {
        this.logisticsAccountId = logisticsAccountId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public List<AccountConfig> getAccountConfigList() {
        return accountConfigList;
    }

    public void setAccountConfigList(List<AccountConfig> accountConfigList) {
        this.accountConfigList = accountConfigList;
    }

    public static class AccountConfig {

        @ApiModelProperty(value = "id, 空为新增，非空为修改", name = "id")
        private Integer id;

        @ApiModelProperty(value = "配置项", name = "key")
        private String key;

        @ApiModelProperty(value = "配置值", name = "value")
        private String value;

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }
}
