package com.nsy.api.tms.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-03-11 14:11:36
 */
@ApiModel(value = "OrderTrackingChannelRequest", description = "订单渠道查询请求")
public class OrderTrackingChannelRequest {

    @ApiModelProperty("订单号")
    @NotEmpty(message = "orderNo不能为空")
    private List<String> orderNoList;

    public List<String> getOrderNoList() {
        return orderNoList;
    }

    public void setOrderNoList(List<String> orderNoList) {
        this.orderNoList = orderNoList;
    }
}

