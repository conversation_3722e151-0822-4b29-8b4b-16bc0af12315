package com.nsy.api.tms.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "TmsLogisticsCountryProvinceListRequest", description = "request")
public class TmsLogisticsCountryProvinceListRequest {
    @ApiModelProperty(value = "物流公司名称", name = "logisticsCompany")
    private String logisticsCompany;
    @ApiModelProperty(value = "国家二字码", name = "countryCode")
    private String countryCode;

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }
}
