package com.nsy.api.tms.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 仓库地区邮编映射新增请求
 *
 * <AUTHOR>
 * @since 1.0
 */
@ApiModel(value = "BdSpaceAreaPostCodeMappingInsertRequest", description = "仓库地区邮编映射新增请求")
public class BdSpaceAreaPostCodeMappingInsertRequest {

    @ApiModelProperty("映射ID（编辑时需要）")
    private Integer id;

    @ApiModelProperty("仓库ID")
    @NotNull(message = "仓库ID不能为空")
    private Integer spaceId;

    @ApiModelProperty("仓库名称")
    @NotBlank(message = "仓库名称不能为空")
    private String spaceName;

    @ApiModelProperty("邮编")
    @NotBlank(message = "邮编不能为空")
    private String postCode;

    @ApiModelProperty("地区名称")
    @NotBlank(message = "地区名称不能为空")
    private String areaName;

    @ApiModelProperty("版本号")
    private Long version;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getSpaceId() {
        return spaceId;
    }

    public void setSpaceId(Integer spaceId) {
        this.spaceId = spaceId;
    }

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }
}
