package com.nsy.api.tms.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@ApiModel(value = "StringListRequest", description = "字符串集合请求")
public class StringListRequest {

    @Size(min = 1, message = "stringList不能为空")
    @NotNull
    @ApiModelProperty(value = "要操作的集合", name = "stringList", required = true)
    private List<String> stringList;

    public List<String> getStringList() {
        return stringList;
    }

    public void setStringList(List<String> stringList) {
        this.stringList = stringList;
    }
}
