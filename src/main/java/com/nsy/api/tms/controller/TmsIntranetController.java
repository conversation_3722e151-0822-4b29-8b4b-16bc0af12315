package com.nsy.api.tms.controller;

import com.nsy.api.tms.dao.entity.TmsLogisticsChannelConfigEntity;
import com.nsy.api.tms.dao.entity.TmsLogisticsTimeReportEntity;
import com.nsy.api.tms.domain.LogisticsChannelConfig;
import com.nsy.api.tms.domain.PackageDispose;
import com.nsy.api.tms.domain.SelectModel;
import com.nsy.api.tms.domain.TmsPackage;
import com.nsy.api.tms.domain.TmsRequestLog;
import com.nsy.api.tms.domain.TmsTagCollectInfo;
import com.nsy.api.tms.domain.TmsTimelinessAnalysis;
import com.nsy.api.tms.domain.TmsTimelinessShippedQtyAnalysisPerMonthByCompany;
import com.nsy.api.tms.domain.TmsTimelinessShippedQtyAnalysisPerMonthByCountry;
import com.nsy.api.tms.enumeration.CustomerCommunicateResultEnum;
import com.nsy.api.tms.enumeration.IsReturnStatusEnum;
import com.nsy.api.tms.enumeration.LogisticsMethodEnum;
import com.nsy.api.tms.enumeration.PackageStatusEnum;
import com.nsy.api.tms.enumeration.ProcessStatusEnum;
import com.nsy.api.tms.request.TmsListRequest;
import com.nsy.api.tms.request.TmsLogisticsTimeRequest;
import com.nsy.api.tms.request.TmsTagCollectRequest;
import com.nsy.api.tms.request.TmsTimelinessAnalysisByCompanyRequest;
import com.nsy.api.tms.request.TmsTimelinessAnalysisByCountryRequest;
import com.nsy.api.tms.response.PageWithStatisticResponse;
import com.nsy.api.tms.response.base.BaseListResponse;
import com.nsy.api.tms.service.PackageDisposeService;
import com.nsy.api.tms.service.PackageService;
import com.nsy.api.tms.service.TmsCountryService;
import com.nsy.api.tms.service.TmsLogisticsChannelConfigService;
import com.nsy.api.tms.service.TmsLogisticsTimeReportService;
import com.nsy.api.tms.service.TmsRequestLogService;
import com.nsy.api.tms.service.TmsTagService;
import com.nsy.api.tms.service.TmsTimelinessAnalysisService;
import com.nsy.api.tms.service.privilege.AccessControlService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.inject.Inject;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RestController
public class TmsIntranetController extends BaseController {

    @Inject
    PackageService packageService;

    @Inject
    TmsLogisticsChannelConfigService tmsLogisticsChannelConfigService;

    @Inject
    TmsRequestLogService tmsRequestLogService;

    @Inject
    PackageDisposeService packageDisposeService;

    @Inject
    TmsTagService tmsTagService;

    @Inject
    AccessControlService accessControlService;

    @Inject
    TmsCountryService tmsCountryService;

    @Inject
    TmsLogisticsTimeReportService logisticsTimeReportService;

    @Inject
    TmsTimelinessAnalysisService timelinessAnalysisService;

    @RequestMapping(value = "/package/list", method = RequestMethod.POST)
    public PageWithStatisticResponse<TmsPackage, TmsPackage> getTmsPackage(@RequestBody TmsListRequest request) {
        return packageService.queryPagePackageByCondition(request);
    }

    @RequestMapping(value = "/logistics-channel/list", method = RequestMethod.GET)
    public List<LogisticsChannelConfig> getLogisticsChannelName(@RequestParam(required = false) String logisticsMethod) {
        return tmsLogisticsChannelConfigService.findLogisticsChannelNameListByStatusAndCompany(TmsLogisticsChannelConfigEntity.ACTIVE, logisticsMethod);
    }

    @RequestMapping(value = "/request-log/list", method = RequestMethod.POST)
    public BaseListResponse<TmsRequestLog> getTmsLog(@RequestBody TmsListRequest request) {
        return tmsRequestLogService.findLogListByCondition(request);
    }


    @RequestMapping(value = "/logistics-method/list", method = RequestMethod.GET)
    public String[] getLogisticsMethod() {
        return LogisticsMethodEnum.getLogisticsMethodArray();
    }

    @RequestMapping(value = "/package/dispose-detail/{logisticsNo}", method = RequestMethod.GET)
    public PackageDispose getPackageDispose(@PathVariable String logisticsNo) {
        return packageDisposeService.getPackageDisposeByLogisticsNo(logisticsNo);
    }

//    @RequestMapping(value = "/package/dispose", method = RequestMethod.POST)
//    public ApiResponse savePackageDispose(@RequestBody TmsPackageDisposeRequest packageDispose) {
//        return packageDisposeService.savePackageDispose(packageDispose);
//    }

    @RequestMapping(value = "/tag/statistics", method = RequestMethod.POST)
    public List<TmsTagCollectInfo> savePackageDispose(@RequestBody TmsTagCollectRequest request) {
        return tmsTagService.collectTmsTag(request);
    }

    @RequestMapping(value = "/package-status/list", method = RequestMethod.GET)
    public String[] getPackageStatus() {
        return PackageStatusEnum.getPackageStatusArray();
    }

    @GetMapping("/process-status/{processType}")
    public List<String> getProcessStatus(@PathVariable String processType) {
        return ProcessStatusEnum.resolve(processType).list().stream()
                .map(ProcessStatusEnum::getDesc).collect(Collectors.toList());
    }

    @GetMapping("/isReturn-status/list")
    public List<String> getIsReturnStatus() {
        return Arrays.stream(IsReturnStatusEnum.values())
                .map(IsReturnStatusEnum::getDesc).collect(Collectors.toList());
    }

    @GetMapping("/communication-result-status/list")
    public List<String> getCommunicationResultStatus() {
        return Arrays.stream(CustomerCommunicateResultEnum.values())
                .map(CustomerCommunicateResultEnum::getDesc).collect(Collectors.toList());
    }


    @RequestMapping(value = "/store-list", method = RequestMethod.GET)
    public BaseListResponse<SelectModel> getStoreList() {
        return accessControlService.storeDataList();
    }

    /**
     *  获取物流时效报表
     */
    @PostMapping("/logistics-time-list")
    public List<TmsLogisticsTimeReportEntity> getLogisticsTimeReportList(@RequestBody TmsLogisticsTimeRequest request) {
        return logisticsTimeReportService.getLogisticsTimeReport(request);
    }

    /**
     *  获取物流时效报表 - 目的国明细报表
     */
    @PostMapping("/logistics-time-detail-list")
    public List<TmsLogisticsTimeReportEntity> getLogisticsTimeDetailReportList(@RequestBody TmsLogisticsTimeRequest request) {
        return logisticsTimeReportService.getLogisticsTimeDetailReport(request);
    }

    @RequestMapping(value = "/country-list", method = RequestMethod.GET)
    public BaseListResponse<SelectModel> getCountryList() {
        return tmsCountryService.countryDataList();
    }

    /**
     * 获取物流时效分析折线图数据（同个物流不同国家的数据）
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/tms-timeliness-analysis-by-company")
    public List<TmsTimelinessShippedQtyAnalysisPerMonthByCompany> getTmsTimelinessAnalysisByCompany(@RequestBody TmsTimelinessAnalysisByCompanyRequest request) throws Exception {
        return timelinessAnalysisService.getTmsTimelinessAnalysisByCompany(request);
    }

    /**
     * 获取物流时效分析表格（同个物流不同国家的数据）
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/tms-timeliness-analysis-by-company/list")
    public List<TmsTimelinessAnalysis> getTmsTimelinessAnalysisByCompanyReport(@RequestBody TmsTimelinessAnalysisByCompanyRequest request) {
        return timelinessAnalysisService.getTmsTimelinessAnalysisByCompanyList(request);
    }

    /**
     * 获取物流时效分析折线图数据（同个国家不同物流的数据）
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/tms-timeliness-analysis-by-country")
    public List<TmsTimelinessShippedQtyAnalysisPerMonthByCountry> getTmsTimelinessAnalysisByCountry(@RequestBody TmsTimelinessAnalysisByCountryRequest request) throws Exception {
        return timelinessAnalysisService.getTmsTimelinessAnalysisByCountry(request);
    }

    /**
     * 获取物流时效分析表格（同个国家不同物流的数据）
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/tms-timeliness-analysis-by-country/list")
    public List<TmsTimelinessAnalysis> getTmsTimelinessAnalysisByCountryReport(@RequestBody TmsTimelinessAnalysisByCountryRequest request) {
        return timelinessAnalysisService.getTmsTimelinessAnalysisByCountryList(request);
    }

    @RequestMapping(value = "/tms-timeliness-analysis/country-list")
    public List<String> getNearly12MonthsCountryList() throws Exception {
        return timelinessAnalysisService.getNearly12MonthsCountryList();
    }
}
