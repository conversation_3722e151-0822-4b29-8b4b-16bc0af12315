package com.nsy.api.tms.controller;

import com.nsy.api.tms.request.OrderTrackingChannelRequest;
import com.nsy.api.tms.request.OrderTrackingRequest;
import com.nsy.api.tms.response.OrderTrackingChannelResponse;
import com.nsy.api.tms.response.OrderTrackingResponse;
import com.nsy.api.tms.service.TrackingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 发货地址表(LogisticsDeliveryAddressMapping)接口
 *
 * <AUTHOR>
 * @since 2024-03-11 14:11:33
 */
@Api(tags = "物流追踪")
@RestController
public class TrackingController extends BaseController {

    @Resource
    private TrackingService trackingService;


    @ApiOperation("订单号追踪")
    @PostMapping("/tracking/order")
    public OrderTrackingResponse orderTracking(@RequestBody @Valid OrderTrackingRequest request) {
        return trackingService.orderTracking(request);
    }

    @ApiOperation("订单号查询物流平台")
    @PostMapping("/tracking/order-channel")
    public List<OrderTrackingChannelResponse> orderTrackingChannel(@RequestBody @Valid OrderTrackingChannelRequest request) {
        return trackingService.orderTrackingChannel(request);
    }


}

