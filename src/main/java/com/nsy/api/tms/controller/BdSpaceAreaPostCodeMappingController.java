package com.nsy.api.tms.controller;

import com.nsy.api.tms.request.BdSpaceAreaPostCodeMappingInsertRequest;
import com.nsy.api.tms.request.BdSpaceAreaPostCodeMappingPageRequest;
import com.nsy.api.tms.response.BdSpaceAreaPostCodeMappingResponse;
import com.nsy.api.tms.response.PageResponse;
import com.nsy.api.tms.service.BdSpaceAreaPostCodeMappingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 仓库地区邮编映射(BdSpaceAreaPostCodeMapping)接口
 *
 * <AUTHOR>
 * @since 1.0
 */
@Api(tags = "仓库地区邮编映射(BdSpaceAreaPostCodeMapping)相关接口")
@RestController
public class BdSpaceAreaPostCodeMappingController extends BaseController {

    @Resource
    private BdSpaceAreaPostCodeMappingService bdSpaceAreaPostCodeMappingService;

    /**
     * 分页查询
     */
    @ApiOperation("分页查询映射关系")
    @PostMapping("/bd-space-area-post-code-mapping/page")
    public PageResponse<BdSpaceAreaPostCodeMappingResponse> queryByPage(@RequestBody BdSpaceAreaPostCodeMappingPageRequest pageRequest) {
        return this.bdSpaceAreaPostCodeMappingService.queryByPage(pageRequest);
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation("通过主键查询单条数据")
    @GetMapping("/bd-space-area-post-code-mapping/{id}")
    public BdSpaceAreaPostCodeMappingResponse queryById(@PathVariable("id") Integer id) {
        return this.bdSpaceAreaPostCodeMappingService.getOneById(id);
    }

    /**
     * 新增数据
     */
    @ApiOperation("新增映射关系")
    @PostMapping("/bd-space-area-post-code-mapping/insert")
    public void add(@RequestBody @Valid BdSpaceAreaPostCodeMappingInsertRequest request) {
        this.bdSpaceAreaPostCodeMappingService.saveOrEdit(request);
    }

    /**
     * 编辑数据
     */
    @ApiOperation("编辑映射关系")
    @PostMapping("/bd-space-area-post-code-mapping/update")
    public void edit(@RequestBody @Valid BdSpaceAreaPostCodeMappingInsertRequest request) {
        this.bdSpaceAreaPostCodeMappingService.saveOrEdit(request);
    }

    /**
     * 删除数据
     */
    @ApiOperation("删除映射关系")
    @DeleteMapping("/bd-space-area-post-code-mapping/{id}")
    public void deleteById(@PathVariable("id") Integer id) {
        this.bdSpaceAreaPostCodeMappingService.deleteMapping(id);
    }

    /**
     * 批量删除数据
     */
    @ApiOperation("批量删除映射关系")
    @PostMapping("/bd-space-area-post-code-mapping/delete-batch")
    public void deleteBatch(@RequestBody List<Integer> ids) {
        this.bdSpaceAreaPostCodeMappingService.deleteMappings(ids);
    }

}
