package com.nsy.api.tms.controller;

import com.nsy.api.tms.domain.LogisticsAccountInfo;
import com.nsy.api.tms.domain.TmsLogisticsAccount;
import com.nsy.api.tms.domain.TmsLogisticsAccountConfig;
import com.nsy.api.tms.domain.shared.SelectIntegerModel;
import com.nsy.api.tms.request.StatusRequest;
import com.nsy.api.tms.request.TmsLogisticsAccountAddRequest;
import com.nsy.api.tms.request.TmsLogisticsAccountConfigAddRequest;
import com.nsy.api.tms.request.TmsLogisticsAccountConfigDeleteRequest;
import com.nsy.api.tms.request.TmsLogisticsAccountListRequest;
import com.nsy.api.tms.response.PageResponse;
import com.nsy.api.tms.service.TmsLogisticsAccountService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * Controller层
 * <AUTHOR>
 * @since 1.0
 */
@Api(tags = "物流账号相关接口")
@RestController
public class TmsLogisticsAccountController extends BaseController {

    @Autowired
    private TmsLogisticsAccountService logisticsAccountService;

    @ApiOperation(value = "物流公司账号列表", produces = "application/json")
    @RequestMapping(value = "/logistics-account/list", method = RequestMethod.POST)
    @ResponseBody
    public PageResponse<TmsLogisticsAccount> getLogisticsAccountList(@RequestBody TmsLogisticsAccountListRequest request, @RequestHeader("location") String location) {
        request.setLocation(location);
        return logisticsAccountService.getLogisticsAccountListByRequest(request);
    }

    @ApiOperation(value = "新增物流公司账号", produces = "application/json")
    @RequestMapping(value = "/logistics-account", method = RequestMethod.POST)
    public void addLogisticsAccount(@RequestBody TmsLogisticsAccountAddRequest request) {
        logisticsAccountService.addLogisticsAccount(request);
    }

    @ApiOperation(value = "物流公司账号修改", produces = "application/json")
    @RequestMapping(value = "/logistics-account/{logisticsAccountId}", method = RequestMethod.PUT)
    public void updateLogisticsAccount(@PathVariable("logisticsAccountId") Integer logisticsAccountId, @RequestBody TmsLogisticsAccountAddRequest request) {
        logisticsAccountService.updateLogisticsAccount(logisticsAccountId, request);
    }

    @ApiOperation(value = "物流公司账号信息", produces = "application/json")
    @RequestMapping(value = "/logistics-account/detail/{logisticsAccountId}", method = RequestMethod.GET)
    public TmsLogisticsAccount updateLogisticsAccount(@PathVariable("logisticsAccountId") Integer logisticsAccountId) {
        return logisticsAccountService.getLogisticsAccountDetail(logisticsAccountId);
    }

    @ApiOperation(value = "物流公司账号启用、停用", produces = "application/json")
    @RequestMapping(value = "/logistics-account/status/{logisticsAccountId}", method = RequestMethod.PUT)
    public void updateLogisticsAccountStatus(@PathVariable("logisticsAccountId") Integer logisticsAccountId, @RequestBody StatusRequest request) {
        logisticsAccountService.updateLogisticsAccountStatus(logisticsAccountId, request);
    }

    @ApiOperation(value = "根据物流公司获取物流账号下拉数据源(启用状态,物流账号id-物流账号名称)", produces = "application/json")
    @RequestMapping(value = "/logistics-account/name-select/{logisticsCompany}", method = RequestMethod.GET)
    @ResponseBody
    public List<SelectIntegerModel> getNameSelectByLogisticsCompany(@PathVariable("logisticsCompany") String logisticsCompany) {
        return logisticsAccountService.getNameSelectByLogisticsCompany(logisticsCompany);
    }

    @ApiOperation(value = "根据物流公司获取物流账号信息", produces = "application/json")
    @RequestMapping(value = "/logistics-account-info/{logisticsCompany}", method = RequestMethod.GET)
    @ResponseBody
    public List<LogisticsAccountInfo> getLogisticsAccountByLogisticsCompany(@PathVariable("logisticsCompany") String logisticsCompany) {
        return logisticsAccountService.getLogisticsAccountByLogisticsCompany(logisticsCompany);
    }

    /*---------------------------------配置接口------------------------------------------------*/

    @ApiOperation(value = "根据物流账号获取配置列表", produces = "application/json")
    @RequestMapping(value = "/logistics-account-config/{logisticsAccountId}", method = RequestMethod.GET)
    @ResponseBody
    public List<TmsLogisticsAccountConfig> getLogisticsAccountConfigListByRequest(@PathVariable Integer logisticsAccountId) {
        return logisticsAccountService.getLogisticsAccountConfigListByRequest(logisticsAccountId);
    }

    @ApiOperation(value = "物流账号配置批量删除", produces = "application/json")
    @RequestMapping(value = "/logistics-account-config/batch-delete", method = RequestMethod.PUT)
    public void deleteLogisticsAccountConfig(@RequestBody TmsLogisticsAccountConfigDeleteRequest request) {
        logisticsAccountService.deleteLogisticsAccountConfig(request);
    }

    @ApiOperation(value = "物流账号配置保存操作", produces = "application/json")
    @RequestMapping(value = "/logistics-account-config", method = RequestMethod.POST)
    public void saveLogisticsAccountConfig(@RequestBody TmsLogisticsAccountConfigAddRequest request) {
        logisticsAccountService.saveLogisticsAccountConfig(request);
    }

}
