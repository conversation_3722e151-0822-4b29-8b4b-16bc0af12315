package com.nsy.api.tms.logistics.ups.ship.response.multiplepieces;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.nsy.api.tms.logistics.ups.ship.response.ResponseType;

public class MultiplePiecesShipmentResponse {
    @JsonProperty(value = "Response")
    private ResponseType responseType;
    @JsonProperty(value = "ShipmentResults")
    private MultiplePiecesShipmentResults shipmentResults;

    public ResponseType getResponseType() {
        return responseType;
    }

    public void setResponseType(ResponseType responseType) {
        this.responseType = responseType;
    }

    public MultiplePiecesShipmentResults getShipmentResults() {
        return shipmentResults;
    }

    public void setShipmentResults(MultiplePiecesShipmentResults shipmentResults) {
        this.shipmentResults = shipmentResults;
    }
}
