package com.nsy.api.tms.logistics.fourpx;

import com.fasterxml.jackson.annotation.JsonProperty;

public class FourPxLogisticsOrderResponse extends FourPxLogisticsBaseResponse {

    @JsonProperty(value = "data")
    private Data data;

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    public static class Data {

        @JsonProperty(value = "ds_consignment_no")
        private String dsConsignmentNo;

        @JsonProperty(value = "4px_tracking_no")
        private String pxTrackingNo;

        @JsonProperty(value = "label_barcode")
        private String labelBarcode;

        @JsonProperty(value = "logistics_channel_name")
        private String logisticsChannelName;

        @JsonProperty(value = "ref_no")
        private String refNo;

        @JsonProperty(value = "logistics_channel_no")
        private String logisticsChannelNo;

        public String getDsConsignmentNo() {
            return dsConsignmentNo;
        }

        public void setDsConsignmentNo(String dsConsignmentNo) {
            this.dsConsignmentNo = dsConsignmentNo;
        }

        public String getPxTrackingNo() {
            return pxTrackingNo;
        }

        public void setPxTrackingNo(String pxTrackingNo) {
            this.pxTrackingNo = pxTrackingNo;
        }

        public String getLabelBarcode() {
            return labelBarcode;
        }

        public void setLabelBarcode(String labelBarcode) {
            this.labelBarcode = labelBarcode;
        }

        public String getLogisticsChannelName() {
            return logisticsChannelName;
        }

        public void setLogisticsChannelName(String logisticsChannelName) {
            this.logisticsChannelName = logisticsChannelName;
        }

        public String getRefNo() {
            return refNo;
        }

        public void setRefNo(String refNo) {
            this.refNo = refNo;
        }

        public String getLogisticsChannelNo() {
            return logisticsChannelNo;
        }

        public void setLogisticsChannelNo(String logisticsChannelNo) {
            this.logisticsChannelNo = logisticsChannelNo;
        }
    }

}
