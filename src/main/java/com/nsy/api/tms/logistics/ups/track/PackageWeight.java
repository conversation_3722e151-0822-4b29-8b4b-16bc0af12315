package com.nsy.api.tms.logistics.ups.track;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @Author: <PERSON>
 * @Date: 2019/1/18 12:58
 */
public class PackageWeight {
    @JsonProperty(value = "UnitOfMeasurement")
    private UnitOfMeasurement unitOfMeasurement;
    @JsonProperty(value = "Weight")
    private String weight;

    public UnitOfMeasurement getUnitOfMeasurement() {
        return unitOfMeasurement;
    }

    public void setUnitOfMeasurement(UnitOfMeasurement unitOfMeasurement) {
        this.unitOfMeasurement = unitOfMeasurement;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }
}
