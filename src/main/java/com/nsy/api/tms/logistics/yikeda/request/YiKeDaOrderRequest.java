package com.nsy.api.tms.logistics.yikeda.request;

import com.fasterxml.jackson.annotation.JsonProperty;

public class YiKeDaOrderRequest extends YiKeDaBaseRequest {
    @JsonProperty(value = "Data")
    private Data data;

    @JsonProperty(value = "RequestId")
    private String requestId;

    @JsonProperty(value = "RequestTime")
    private String requestTime;

    public static class Data {
        @JsonProperty("OrderID")
        private String orderID; // 客户订单号，不可重复使用

        @JsonProperty(value = "ParcelInformation")
        private ParcelInformation parcelInformation; // 包裹信息

        @JsonProperty(value = "RecipientAddress")
        private RecipientAddress recipientAddress; // 收货地址

        @JsonProperty(value = "ChannelName")
        private String channelName; //  渠道名称

        @JsonProperty(value = "Token")
        private String token; // 若进行了地址验证，则使用其返回 的标识;默认 32 个 9

        @JsonProperty(value = "ServiceTypeCode")
        private String serviceTypeCode; // STC 服务类型代码

        @JsonProperty(value = "WarehouseCode")
        private String warehouseCode; // 渠道仓库代码， 参考附件

        public String getOrderID() {
            return orderID;
        }

        public void setOrderID(String orderID) {
            this.orderID = orderID;
        }

        public ParcelInformation getParcelInformation() {
            return parcelInformation;
        }

        public void setParcelInformation(ParcelInformation parcelInformation) {
            this.parcelInformation = parcelInformation;
        }

        public RecipientAddress getRecipientAddress() {
            return recipientAddress;
        }

        public void setRecipientAddress(RecipientAddress recipientAddress) {
            this.recipientAddress = recipientAddress;
        }

        public String getChannelName() {
            return channelName;
        }

        public void setChannelName(String channelName) {
            this.channelName = channelName;
        }

        public String getToken() {
            return token;
        }

        public void setToken(String token) {
            this.token = token;
        }

        public String getServiceTypeCode() {
            return serviceTypeCode;
        }

        public void setServiceTypeCode(String serviceTypeCode) {
            this.serviceTypeCode = serviceTypeCode;
        }

        public String getWarehouseCode() {
            return warehouseCode;
        }

        public void setWarehouseCode(String warehouseCode) {
            this.warehouseCode = warehouseCode;
        }
    }

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(String requestTime) {
        this.requestTime = requestTime;
    }
}
