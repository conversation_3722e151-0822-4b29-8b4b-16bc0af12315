package com.nsy.api.tms.logistics.tnt.request.ship;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * @Author: <PERSON> Lee
 * @Date: 2019/1/21 16:22
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "BOOK")
public class Book {
    @XmlElement(name = "CONREF")
    private String conRef;
    @XmlAttribute(name = "EMAILREQD")
    private String emailreqd;
    @XmlAttribute(name = "ShowBookingRef")
    private String showBooking;

    public String getEmailreqd() {
        return emailreqd;
    }

    public void setEmailreqd(String emailreqd) {
        this.emailreqd = emailreqd;
    }

    public String getShowBookingRef() {
        return showBooking;
    }

    public void setShowBookingRef(String showBooking) {
        this.showBooking = showBooking;
    }

    public String getConRef() {
        return conRef;
    }

    public void setConRef(String conRef) {
        this.conRef = conRef;
    }

}
