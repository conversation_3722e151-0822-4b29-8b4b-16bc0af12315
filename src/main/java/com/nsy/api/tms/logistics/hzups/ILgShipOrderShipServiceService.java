package com.nsy.api.tms.logistics.hzups;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import javax.xml.ws.Service;

/**
 * This class was generated by Apache CXF 3.2.14
 * 2021-04-13T17:33:47.190+08:00
 * Generated source version: 3.2.14
 *
 */
@WebServiceClient(name = "ILgShipOrderShipServiceService",
                  wsdlLocation = "http://dd10.rui-y.com:8401/lgtbws/eship/orderShip?wsdl",
                  targetNamespace = "http://service.eship.logisticstb/")
public class ILgShipOrderShipServiceService extends Service {

    public final static URL WSDL_LOCATION;

    public final static QName SERVICE = new QName("http://service.eship.logisticstb/", "ILgShipOrderShipServiceService");
    public final static QName ILgShipOrderShipServicePort = new QName("http://service.eship.logisticstb/", "ILgShipOrderShipServicePort");
    static {
        URL url = null;
        try {
            url = new URL("http://dd10.rui-y.com:8401/lgtbws/eship/orderShip?wsdl");
        } catch (MalformedURLException e) {
            java.util.logging.Logger.getLogger(ILgShipOrderShipServiceService.class.getName())
                .log(java.util.logging.Level.INFO,
                     "Can not initialize the default wsdl from {0}", "http://dd10.rui-y.com:8401/lgtbws/eship/orderShip?wsdl");
        }
        WSDL_LOCATION = url;
    }

    public ILgShipOrderShipServiceService(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public ILgShipOrderShipServiceService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public ILgShipOrderShipServiceService() {
        super(WSDL_LOCATION, SERVICE);
    }

    public ILgShipOrderShipServiceService(WebServiceFeature ... features) {
        super(WSDL_LOCATION, SERVICE, features);
    }

    public ILgShipOrderShipServiceService(URL wsdlLocation, WebServiceFeature ... features) {
        super(wsdlLocation, SERVICE, features);
    }

    public ILgShipOrderShipServiceService(URL wsdlLocation, QName serviceName, WebServiceFeature ... features) {
        super(wsdlLocation, serviceName, features);
    }




    /**
     *
     * @return
     *     returns ILgShipOrderShipService
     */
    @WebEndpoint(name = "ILgShipOrderShipServicePort")
    public ILgShipOrderShipService getILgShipOrderShipServicePort() {
        return super.getPort(ILgShipOrderShipServicePort, ILgShipOrderShipService.class);
    }

    /**
     *
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns ILgShipOrderShipService
     */
    @WebEndpoint(name = "ILgShipOrderShipServicePort")
    public ILgShipOrderShipService getILgShipOrderShipServicePort(WebServiceFeature... features) {
        return super.getPort(ILgShipOrderShipServicePort, ILgShipOrderShipService.class, features);
    }

}
