package com.nsy.api.tms.logistics.zuohai;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * HXD
 * 2024/1/11
 **/

public class ReplenishmentProduct {
    @JsonProperty("ck_sku")
    private String ckSku;

    @JsonProperty("zxh")
    private String zxh;

    @JsonProperty("nums")
    private String nums;

    @JsonProperty("weight")
    private String weight;

    @JsonProperty("cc")
    private String cc;

    @JsonProperty("kk")
    private String kk;

    @JsonProperty("gg")
    private String gg;

    private String orderNo;

    private String spaceName;

    public String getSpaceName() {
        return spaceName;
    }

    public void setSpaceName(String spaceName) {
        this.spaceName = spaceName;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getCkSku() {
        return ckSku;
    }

    public void setCkSku(String ckSku) {
        this.ckSku = ckSku;
    }

    public String getZxh() {
        return zxh;
    }

    public void setZxh(String zxh) {
        this.zxh = zxh;
    }

    public String getNums() {
        return nums;
    }

    public void setNums(String nums) {
        this.nums = nums;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getCc() {
        return cc;
    }

    public void setCc(String cc) {
        this.cc = cc;
    }

    public String getKk() {
        return kk;
    }

    public void setKk(String kk) {
        this.kk = kk;
    }

    public String getGg() {
        return gg;
    }

    public void setGg(String gg) {
        this.gg = gg;
    }
}
