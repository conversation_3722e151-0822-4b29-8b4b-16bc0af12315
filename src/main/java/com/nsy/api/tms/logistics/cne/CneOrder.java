package com.nsy.api.tms.logistics.cne;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

public class CneOrder {
    @JSONField(name = "iID")
    private Integer preRecordId;
    @JSONField(name = "nItemType")
    private Integer expressType;
    @JSONField(name = "cRNo")
    private String orderId;
    @JSONField(name = "cDes")
    private String cDes; //ISO 二字代码
    @JSONField(name = "cEmsKind")
    private String cEmsKind;
    @JSONField(name = "fWeight")
    private Double weight;
    @JSONField(name = "cReceiver")
    private String receiver;
    @JSONField(name = "cRPhone")
    private String phone;
    @JSONField(name = "cREMail")
    private String email;
    @JSONField(name = "cRPostcode")
    private String postCode;
    @JSONField(name = "cRCountry")
    private String country;
    @JSONField(name = "cRProvince")
    private String province;
    @JSONField(name = "cRCity")
    private String city;
    @JSONField(name = "cRAddr")
    private String addr;
    @JSO<PERSON>ield(name = "GoodsList")
    List<Item> goodsList;
    @JSO<PERSON>ield(name = "cRTaxCode")
    private String cRTaxCode;
    @JSONField(name = "vatCode")
    private String vatCode;
    @JSONField(name = "iossCode")
    private String iossCode;

    public Integer getPreRecordId() {
        return preRecordId;
    }

    public void setPreRecordId(Integer preRecordId) {
        this.preRecordId = preRecordId;
    }

    public Integer getExpressType() {
        return expressType;
    }

    public void setExpressType(Integer expressType) {
        this.expressType = expressType;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getcDes() {
        return cDes;
    }

    public void setcDes(String cDes) {
        this.cDes = cDes;
    }

    public String getcEmsKind() {
        return cEmsKind;
    }

    public void setcEmsKind(String cEmsKind) {
        this.cEmsKind = cEmsKind;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getAddr() {
        return addr;
    }

    public void setAddr(String addr) {
        this.addr = addr;
    }

    public List<Item> getGoodsList() {
        return goodsList;
    }

    public void setGoodsList(List<Item> goodsList) {
        this.goodsList = goodsList;
    }

    public String getcRTaxCode() {
        return cRTaxCode;
    }

    public void setcRTaxCode(String cRTaxCode) {
        this.cRTaxCode = cRTaxCode;
    }

    public String getVatCode() {
        return vatCode;
    }

    public void setVatCode(String vatCode) {
        this.vatCode = vatCode;
    }

    public String getIossCode() {
        return iossCode;
    }

    public void setIossCode(String iossCode) {
        this.iossCode = iossCode;
    }
}
