
package com.nsy.api.tms.logistics.hzups;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the logisticstb.eship.service package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _Exception_QNAME = new QName("http://service.eship.logisticstb/", "Exception");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: logisticstb.eship.service
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link java.lang.Exception }
     * 
     */
    public Exception createException() {
        return new Exception();
    }

    /**
     * Create an instance of {@link OrderShipRequest }
     * 
     */
    public OrderShipRequest createOrderShipRequest() {
        return new OrderShipRequest();
    }

    /**
     * Create an instance of {@link BaseEShipRequest }
     * 
     */
    public BaseEShipRequest createBaseEShipRequest() {
        return new BaseEShipRequest();
    }

    /**
     * Create an instance of {@link Order }
     * 
     */
    public Order createOrder() {
        return new Order();
    }

    /**
     * Create an instance of {@link FredutyContact }
     * 
     */
    public FredutyContact createFredutyContact() {
        return new FredutyContact();
    }

    /**
     * Create an instance of {@link OrderInvoice }
     * 
     */
    public OrderInvoice createOrderInvoice() {
        return new OrderInvoice();
    }

    /**
     * Create an instance of {@link OrderInvoiceDetail }
     * 
     */
    public OrderInvoiceDetail createOrderInvoiceDetail() {
        return new OrderInvoiceDetail();
    }

    /**
     * Create an instance of {@link Label }
     * 
     */
    public Label createLabel() {
        return new Label();
    }

    /**
     * Create an instance of {@link OrderPackage }
     * 
     */
    public OrderPackage createOrderPackage() {
        return new OrderPackage();
    }

    /**
     * Create an instance of {@link Contact }
     * 
     */
    public Contact createContact() {
        return new Contact();
    }

    /**
     * Create an instance of {@link Address }
     * 
     */
    public Address createAddress() {
        return new Address();
    }

    /**
     * Create an instance of {@link TaxdutyContact }
     * 
     */
    public TaxdutyContact createTaxdutyContact() {
        return new TaxdutyContact();
    }

    /**
     * Create an instance of {@link ClientInfo }
     * 
     */
    public ClientInfo createClientInfo() {
        return new ClientInfo();
    }

    /**
     * Create an instance of {@link OrderShipResponse }
     * 
     */
    public OrderShipResponse createOrderShipResponse() {
        return new OrderShipResponse();
    }

    /**
     * Create an instance of {@link BaseResponse }
     * 
     */
    public BaseResponse createBaseResponse() {
        return new BaseResponse();
    }

    /**
     * Create an instance of {@link OrderShipResult }
     * 
     */
    public OrderShipResult createOrderShipResult() {
        return new OrderShipResult();
    }

    /**
     * Create an instance of {@link LgOrderLabelOutputDetailVO }
     * 
     */
    public LgOrderLabelOutputDetailVO createLgOrderLabelOutputDetailVO() {
        return new LgOrderLabelOutputDetailVO();
    }

    /**
     * Create an instance of {@link BaseVO }
     * 
     */
    public BaseVO createBaseVO() {
        return new BaseVO();
    }

    /**
     * Create an instance of {@link BaseUserVO }
     * 
     */
    public BaseUserVO createBaseUserVO() {
        return new BaseUserVO();
    }

    /**
     * Create an instance of {@link BaseUser }
     * 
     */
    public BaseUser createBaseUser() {
        return new BaseUser();
    }

    /**
     * Create an instance of {@link BaseEntity }
     * 
     */
    public BaseEntity createBaseEntity() {
        return new BaseEntity();
    }

    /**
     * Create an instance of {@link LgOrderLabelUrlDetailVO }
     * 
     */
    public LgOrderLabelUrlDetailVO createLgOrderLabelUrlDetailVO() {
        return new LgOrderLabelUrlDetailVO();
    }

    /**
     * Create an instance of {@link OrderPackageResult }
     * 
     */
    public OrderPackageResult createOrderPackageResult() {
        return new OrderPackageResult();
    }

    /**
     * Create an instance of {@link Message }
     * 
     */
    public Message createMessage() {
        return new Message();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link java.lang.Exception }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://service.eship.logisticstb/", name = "Exception")
    public JAXBElement<Exception> createException(Exception value) {
        return new JAXBElement<Exception>(_Exception_QNAME, Exception.class, null, value);
    }

}
