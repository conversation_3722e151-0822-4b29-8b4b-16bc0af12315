package com.nsy.api.tms.logistics.tnt.request.ship;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * @Author: <PERSON>
 * @Date: 2019/1/21 16:31
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "LABEL")
public class Label {
    @XmlElement(name = "CONREF")
    private String conRef;

    public String getConRef() {
        return conRef;
    }

    public void setConRef(String conRef) {
        this.conRef = conRef;
    }

}
