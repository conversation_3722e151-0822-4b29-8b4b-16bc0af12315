
package com.nsy.api.tms.logistics.ups.paperless;

import com.alibaba.fastjson.annotation.JSONField;


public class TransactionReferenceType {

    @JSONField(name = "CustomerContext")
    private String customerContext;

    @JSONField(name = "TransactionIdentifier")
    private String transactionIdentifier;

    /**
     *
     * @return possible object is
     * {@link String }
     */
    public String getCustomerContext() {
        return customerContext;
    }

    /**
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCustomerContext(String value) {
        this.customerContext = value;
    }

    /**
     *
     * @return possible object is
     * {@link String }
     */
    public String getTransactionIdentifier() {
        return transactionIdentifier;
    }

    /**
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setTransactionIdentifier(String value) {
        this.transactionIdentifier = value;
    }

}
