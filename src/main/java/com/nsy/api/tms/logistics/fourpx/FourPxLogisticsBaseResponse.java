package com.nsy.api.tms.logistics.fourpx;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class FourPxLogisticsBaseResponse {

    @JsonProperty(value = "msg")
    private String msg;

    @JsonProperty(value = "result")
    private String result;

    @JsonProperty(value = "errors")
    private List<Errors> errors;

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getMsg() {
        return this.msg;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getResult() {
        return this.result;
    }

    public void setErrors(List<Errors> errors) {
        this.errors = errors;
    }

    public List<Errors> getErrors() {
        return this.errors;
    }

    public static class Errors {

        @JsonProperty(value = "error_code")
        private String errorCode;

        @JsonProperty(value = "error_msg")
        private String errorMsg;

        @JsonProperty(value = "reference_code")
        private String referenceCode;

        public String getErrorCode() {
            return errorCode;
        }

        public void setErrorCode(String errorCode) {
            this.errorCode = errorCode;
        }

        public String getErrorMsg() {
            return errorMsg;
        }

        public void setErrorMsg(String errorMsg) {
            this.errorMsg = errorMsg;
        }

        public String getReferenceCode() {
            return referenceCode;
        }

        public void setReferenceCode(String referenceCode) {
            this.referenceCode = referenceCode;
        }
    }
}
