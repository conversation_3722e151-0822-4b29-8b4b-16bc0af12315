package com.nsy.api.tms.logistics.epacket.request;


import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import java.util.List;

@XmlAccessorType(XmlAccessType.NONE)
@XmlRootElement(name = "order")
@XmlType(propOrder = {"orderId", "operationType", "productType", "customerCode", "vipCode", "clctType", "pod"
    , "untread", "volWeight", "startDate", "endDate", "sku1", "sku2", "barCode", "printCode",
    "ePacketSender", "ePacketReceiver", "ePacketCollector", "ePacketOrderItemInfo", "remark" })
public class EPacketOrderInfo {
    @XmlElement(name = "orderid")
    private String orderId;

    @XmlElement(name = "operationtype")
    private Integer operationType;

    @XmlElement(name = "producttype")
    private Integer productType;

    @XmlElement(name = "customercode")
    private String customerCode;

    @XmlElement(name = "vipcode")
    private String vipCode;

    @XmlElement(name = "clcttype")
    private Integer clctType;

    @XmlElement(name = "pod")
    private Boolean pod;

    @XmlElement(name = "untread")
    private String untread;

    @XmlElement(name = "volweight")
    private Integer volWeight;

    @XmlElement(name = "startdate")
    private String startDate;

    @XmlElement(name = "enddate")
    private String endDate;

    @XmlElement(name = "remark")
    private String remark;

    @XmlElement(name = "sku1")
    private String sku1;

    @XmlElement(name = "sku2")
    private String sku2;

    @XmlElement(name = "barcode")
    private String barCode;

    @XmlElement(name = "printcode")
    private String printCode;

    private EPacketSender ePacketSender;

    private EPacketReceiver ePacketReceiver;

    private EPacketCollector ePacketCollector;

    private List<EPacketOrderItemInfo> ePacketOrderItemInfo;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    public Integer getProductType() {
        return productType;
    }

    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getVipCode() {
        return vipCode;
    }

    public void setVipCode(String vipCode) {
        this.vipCode = vipCode;
    }

    public Integer getClctType() {
        return clctType;
    }

    public void setClctType(Integer clctType) {
        this.clctType = clctType;
    }

    public Boolean getPod() {
        return pod;
    }

    public void setPod(Boolean pod) {
        this.pod = pod;
    }

    public String getUntread() {
        return untread;
    }

    public void setUntread(String untread) {
        this.untread = untread;
    }

    public Integer getVolWeight() {
        return volWeight;
    }

    public void setVolWeight(Integer volWeight) {
        this.volWeight = volWeight;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSku1() {
        return sku1;
    }

    public void setSku1(String sku1) {
        this.sku1 = sku1;
    }

    public String getSku2() {
        return sku2;
    }

    public void setSku2(String sku2) {
        this.sku2 = sku2;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public String getPrintCode() {
        return printCode;
    }

    public void setPrintCode(String printCode) {
        this.printCode = printCode;
    }

    public EPacketSender getePacketSender() {
        return ePacketSender;
    }

    @XmlElement(name = "sender")
    public void setePacketSender(EPacketSender ePacketSender) {
        this.ePacketSender = ePacketSender;
    }

    public EPacketReceiver getePacketReceiver() {
        return ePacketReceiver;
    }

    @XmlElement(name = "receiver")
    public void setePacketReceiver(EPacketReceiver ePacketReceiver) {
        this.ePacketReceiver = ePacketReceiver;
    }

    public EPacketCollector getePacketCollector() {
        return ePacketCollector;
    }

    @XmlElement(name = "collect")
    public void setePacketCollector(EPacketCollector ePacketCollector) {
        this.ePacketCollector = ePacketCollector;
    }

    public List<EPacketOrderItemInfo> getePacketOrderItemInfo() {
        return ePacketOrderItemInfo;
    }

    @XmlElementWrapper(name = "items")
    @XmlElement(name = "item")
    public void setePacketOrderItemInfo(List<EPacketOrderItemInfo> ePacketOrderItemInfo) {
        this.ePacketOrderItemInfo = ePacketOrderItemInfo;
    }
}
