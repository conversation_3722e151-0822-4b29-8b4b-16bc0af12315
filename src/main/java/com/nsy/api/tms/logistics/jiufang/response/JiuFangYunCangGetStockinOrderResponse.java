package com.nsy.api.tms.logistics.jiufang.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @version: v1.0
 * @date 2025-03-27 9:42
 */
public class JiuFangYunCangGetStockinOrderResponse {

    /**
     * 海外仓入库单号
     */
    @JsonProperty("receiving_code")
    private String receivingCode;

    /**
     * 客户单号-时颖订单号
     */
    @JsonProperty("reference_no")
    private String referenceNo;

    /**
     * 入库单状态 C:新建 W:头程在途 P:头程收货中 T:头程收货完成 Z:转运中 G:目的仓库收货中 F:目的仓收货完成 E:完成上架 X:废弃
     */
    @JsonProperty("receiving_status")
    private String receivingStatus;

    @JsonProperty("items")
    private List<JiuFangYunCangGetStockinOrderItemResponse> items;


    public String getReceivingCode() {
        return receivingCode;
    }

    public void setReceivingCode(String receivingCode) {
        this.receivingCode = receivingCode;
    }

    public String getReferenceNo() {
        return referenceNo;
    }

    public void setReferenceNo(String referenceNo) {
        this.referenceNo = referenceNo;
    }

    public String getReceivingStatus() {
        return receivingStatus;
    }

    public void setReceivingStatus(String receivingStatus) {
        this.receivingStatus = receivingStatus;
    }

    public List<JiuFangYunCangGetStockinOrderItemResponse> getItems() {
        return items;
    }

    public void setItems(List<JiuFangYunCangGetStockinOrderItemResponse> items) {
        this.items = items;
    }
}
