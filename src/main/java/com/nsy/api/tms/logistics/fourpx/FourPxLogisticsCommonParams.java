package com.nsy.api.tms.logistics.fourpx;


import com.nsy.api.tms.utils.Md5Utils;

public class FourPxLogisticsCommonParams {

    private String appKey;

    private String format;

    private String method;

    private Long timeStamp;

    private String version;

    private String body;

    private String sign;

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public Long getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(Long timeStamp) {
        this.timeStamp = timeStamp;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public void initSign(String appSecret) {
        String str = "app_key" + this.appKey + "format" + this.format + "method" + this.method + "timestamp" + this.timeStamp + "v" + this.version + body + appSecret;
        this.setSign(Md5Utils.getMD5Str(str));
    }
}
