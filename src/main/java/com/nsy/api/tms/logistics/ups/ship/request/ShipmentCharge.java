package com.nsy.api.tms.logistics.ups.ship.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @Author: <PERSON>
 * @Date: 2019/1/19 19:09
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ShipmentCharge {
    @JsonProperty(value = "Type")
    private String type;
    @JsonProperty(value = "BillShipper")
    private BillShipper billShipper;
    @JsonProperty(value = "BillReceiver")
    private BillShipper billReceiver;

    public BillShipper getBillReceiver() {
        return billReceiver;
    }

    public void setBillReceiver(BillShipper billReceiver) {
        this.billReceiver = billReceiver;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public BillShipper getBillShipper() {
        return billShipper;
    }

    public void setBillShipper(BillShipper billShipper) {
        this.billShipper = billShipper;
    }
}
