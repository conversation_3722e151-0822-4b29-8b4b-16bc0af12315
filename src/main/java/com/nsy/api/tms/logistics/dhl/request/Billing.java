package com.nsy.api.tms.logistics.dhl.request;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlAccessorType(XmlAccessType.NONE)
@XmlRootElement(name = "Billing")
public class Billing {
    @XmlElement(name = "ShipperAccountNumber")
    private String shipperAccountNumber; // 账户号码
    @XmlElement(name = "ShippingPaymentType")
    private String shippingPaymentType; // 付款类型
    @XmlElement(name = "BillingAccountNumber")
    private String billingAccountNumber; // 第三方付款账号
    @XmlElement(name = "DutyAccountNumber")
    private String dutyAccountNumber; // 目的地税金支付账号

    public String getDutyAccountNumber() {
        return dutyAccountNumber;
    }

    public void setDutyAccountNumber(String dutyAccountNumber) {
        this.dutyAccountNumber = dutyAccountNumber;
    }

    public String getShipperAccountNumber() {
        return shipperAccountNumber;
    }

    public void setShipperAccountNumber(String shipperAccountNumber) {
        this.shipperAccountNumber = shipperAccountNumber;
    }

    public String getShippingPaymentType() {
        return shippingPaymentType;
    }

    public void setShippingPaymentType(String shippingPaymentType) {
        this.shippingPaymentType = shippingPaymentType;
    }

    public String getBillingAccountNumber() {
        return billingAccountNumber;
    }

    public void setBillingAccountNumber(String billingAccountNumber) {
        this.billingAccountNumber = billingAccountNumber;
    }

}
