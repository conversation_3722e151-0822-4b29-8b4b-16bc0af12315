package com.nsy.api.tms.logistics.ups.track;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @Author: <PERSON>
 * @Date: 2019/1/18 10:41
 */
public class TrackRequest {
    @JsonProperty(value = "Request")
    private TrackRequestDetail trackRequestDetail;
    @JsonProperty(value = "InquiryNumber")
    private String inquiryNumber;

    public TrackRequestDetail getTrackRequestDetail() {
        return trackRequestDetail;
    }

    public void setTrackRequestDetail(TrackRequestDetail trackRequestDetail) {
        this.trackRequestDetail = trackRequestDetail;
    }

    public String getInquiryNumber() {
        return inquiryNumber;
    }

    public void setInquiryNumber(String inquiryNumber) {
        this.inquiryNumber = inquiryNumber;
    }
}
