package com.nsy.api.tms.logistics.kts.request;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Request")
public class KtsRoute {
    @XmlElement(name = "sfWaybillNo")
    private String sfWaybillNo;

    @XmlElement(name = "orderId")
    private String orderId;

    public String getSfWaybillNo() {
        return sfWaybillNo;
    }

    public void setSfWaybillNo(String sfWaybillNo) {
        this.sfWaybillNo = sfWaybillNo;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }
}
