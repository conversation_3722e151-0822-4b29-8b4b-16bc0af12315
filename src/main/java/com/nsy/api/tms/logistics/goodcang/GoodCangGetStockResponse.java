package com.nsy.api.tms.logistics.goodcang;

import com.nsy.api.tms.logistics.goodcang.stockin.GoodCangBaseResponse;

import java.util.ArrayList;
import java.util.List;

/**
 * HXD
 * 2022/7/11
 **/
public class GoodCangGetStockResponse extends GoodCangBaseResponse {

    private List<GoodCangStockDto> data = new ArrayList<>();

    private int count;

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public List<GoodCangStockDto> getData() {
        return data;
    }

    public void setData(List<GoodCangStockDto> data) {
        this.data = data;
    }
}
