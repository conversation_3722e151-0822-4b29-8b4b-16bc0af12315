package com.nsy.api.tms.logistics.toms.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public class TomsLabelRequest extends TomsBaseRequest {

    @JsonProperty(value = "configInfo")
    private ConfigInfo configInfo;

    @JsonProperty(value = "listorder")
    private List<LabelOrder> orderList;


    public ConfigInfo getConfigInfo() {
        return configInfo;
    }

    public void setConfigInfo(ConfigInfo configInfo) {
        this.configInfo = configInfo;
    }

    public List<LabelOrder> getOrderList() {
        return orderList;
    }

    public void setOrderList(List<LabelOrder> orderList) {
        this.orderList = orderList;
    }
}
