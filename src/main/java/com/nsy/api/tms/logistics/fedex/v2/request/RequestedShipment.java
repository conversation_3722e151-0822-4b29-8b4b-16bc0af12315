package com.nsy.api.tms.logistics.fedex.v2.request;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;

import java.util.Date;
import java.util.List;


public class RequestedShipment {

    private String shipDatestamp = DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);
    private String pickupType = "USE_SCHEDULED_PICKUP";
    private String serviceType;
    private String packagingType;
    private boolean blockInsightVisibility = false;
    private LabelSpecification labelSpecification;
    private UserContactInfo shipper;
    private List<UserContactInfo> recipients;
    private ShippingChargesPayment shippingChargesPayment;
    private CustomsClearanceDetail customsClearanceDetail;
    private Integer totalPackageCount;
    private List<RequestedPackageLineItems> requestedPackageLineItems;
    private ShipmentSpecialService shipmentSpecialServices;
    private ShippingDocumentSpecification shippingDocumentSpecification;
    private MasterTracking masterTrackingId;

    public MasterTracking getMasterTrackingId() {
        return masterTrackingId;
    }

    public void setMasterTrackingId(MasterTracking masterTrackingId) {
        this.masterTrackingId = masterTrackingId;
    }

    public ShippingDocumentSpecification getShippingDocumentSpecification() {
        return shippingDocumentSpecification;
    }

    public void setShippingDocumentSpecification(ShippingDocumentSpecification shippingDocumentSpecification) {
        this.shippingDocumentSpecification = shippingDocumentSpecification;
    }

    public ShipmentSpecialService getShipmentSpecialServices() {
        return shipmentSpecialServices;
    }

    public void setShipmentSpecialServices(ShipmentSpecialService shipmentSpecialServices) {
        this.shipmentSpecialServices = shipmentSpecialServices;
    }

    public String getShipDatestamp() {
        return shipDatestamp;
    }

    public void setShipDatestamp(String shipDatestamp) {
        this.shipDatestamp = shipDatestamp;
    }

    public String getPickupType() {
        return pickupType;
    }

    public void setPickupType(String pickupType) {
        this.pickupType = pickupType;
    }

    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }

    public String getPackagingType() {
        return packagingType;
    }

    public void setPackagingType(String packagingType) {
        this.packagingType = packagingType;
    }

    public boolean getBlockInsightVisibility() {
        return blockInsightVisibility;
    }

    public void setBlockInsightVisibility(boolean blockInsightVisibility) {
        this.blockInsightVisibility = blockInsightVisibility;
    }

    public LabelSpecification getLabelSpecification() {
        return labelSpecification;
    }

    public void setLabelSpecification(LabelSpecification labelSpecification) {
        this.labelSpecification = labelSpecification;
    }

    public boolean isBlockInsightVisibility() {
        return blockInsightVisibility;
    }

    public UserContactInfo getShipper() {
        return shipper;
    }

    public void setShipper(UserContactInfo shipper) {
        this.shipper = shipper;
    }

    public List<UserContactInfo> getRecipients() {
        return recipients;
    }

    public void setRecipients(List<UserContactInfo> recipients) {
        this.recipients = recipients;
    }

    public ShippingChargesPayment getShippingChargesPayment() {
        return shippingChargesPayment;
    }

    public void setShippingChargesPayment(ShippingChargesPayment shippingChargesPayment) {
        this.shippingChargesPayment = shippingChargesPayment;
    }

    public CustomsClearanceDetail getCustomsClearanceDetail() {
        return customsClearanceDetail;
    }

    public void setCustomsClearanceDetail(CustomsClearanceDetail customsClearanceDetail) {
        this.customsClearanceDetail = customsClearanceDetail;
    }

    public Integer getTotalPackageCount() {
        return totalPackageCount;
    }

    public void setTotalPackageCount(Integer totalPackageCount) {
        this.totalPackageCount = totalPackageCount;
    }

    public List<RequestedPackageLineItems> getRequestedPackageLineItems() {
        return requestedPackageLineItems;
    }

    public void setRequestedPackageLineItems(List<RequestedPackageLineItems> requestedPackageLineItems) {
        this.requestedPackageLineItems = requestedPackageLineItems;
    }

}
