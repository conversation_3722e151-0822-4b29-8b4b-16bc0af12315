package com.nsy.api.tms.logistics.deqi.request;

import com.fasterxml.jackson.annotation.JsonProperty;

public class DeQiOrderRequest extends DeQiBaseRequest {

    @JsonProperty(value = "orders")
    private Order[] orders;

    public static class Order {

        @JsonProperty(value = "reference_order_number")
        private String referenceOrderNumber; // 客户参考号

        @JsonProperty(value = "ship_channel_code")
        private String shipChannelCode; // 发货方式

        @JsonProperty(value = "total_declare_value")
        private Float totalDeclareValue; //  申报总价值

        @JsonProperty(value = "total_weight")
        private Float totalWeight; // 包裹总重量

        @JsonProperty(value = "length")
        private Float length;  // 长

        @JsonProperty(value = "width")
        private Float width; //  宽

        @JsonProperty(value = "height")
        private Float height; // 高

        @JsonProperty(value = "recipient_name")
        private String recipientName;  // 收件人姓名

        @JsonProperty(value = "house_number")
        private String houseNumber; //  门牌号（有些渠道必填）

        @JsonProperty(value = "tax_id")
        private String taxId;  // 税号

        @JsonProperty(value = "recipient_address1")
        private String recipientAddress1; // 收件人地址一

        @JsonProperty(value = "recipient_country")
        private String recipientCountry; //  收件人国家

        @JsonProperty(value = "recipient_state")
        private String recipientState; // 收件人州/省

        @JsonProperty(value = "recipient_city")
        private String recipientCity;  // 收件人城市

        @JsonProperty(value = "recipient_zip")
        private String recipientZip; // 收件人邮编

        @JsonProperty(value = "recipient_phone")
        private String recipientPhone; //  收件人电话

        @JsonProperty(value = "recipient_email")
        private String recipientEmail; // 收件人邮箱

        @JsonProperty(value = "items")
        private Item[] items; // 货品列表

        public static class Item {
            @JsonProperty(value = "quantity")
            private Integer quantity; // 数量

            @JsonProperty(value = "declare_name")
            private String declareName; //  英文申报名称

            @JsonProperty(value = "declare_value")
            private Float declareValue; // 申报价值

            @JsonProperty(value = "hs_code")
            private String hsCode; // 海关编码

            @JsonProperty(value = "declare_name_cn")
            private String declareNameCn; // 中文申报名称

            public Integer getQuantity() {
                return quantity;
            }

            public void setQuantity(Integer quantity) {
                this.quantity = quantity;
            }

            public String getDeclareName() {
                return declareName;
            }

            public void setDeclareName(String declareName) {
                this.declareName = declareName;
            }

            public Float getDeclareValue() {
                return declareValue;
            }

            public void setDeclareValue(Float declareValue) {
                this.declareValue = declareValue;
            }

            public String getHsCode() {
                return hsCode;
            }

            public void setHsCode(String hsCode) {
                this.hsCode = hsCode;
            }

            public String getDeclareNameCn() {
                return declareNameCn;
            }

            public void setDeclareNameCn(String declareNameCn) {
                this.declareNameCn = declareNameCn;
            }
        }

        public String getReferenceOrderNumber() {
            return referenceOrderNumber;
        }

        public void setReferenceOrderNumber(String referenceOrderNumber) {
            this.referenceOrderNumber = referenceOrderNumber;
        }

        public String getShipChannelCode() {
            return shipChannelCode;
        }

        public void setShipChannelCode(String shipChannelCode) {
            this.shipChannelCode = shipChannelCode;
        }
        

        public Float getTotalDeclareValue() {
            return totalDeclareValue;
        }

        public void setTotalDeclareValue(Float totalDeclareValue) {
            this.totalDeclareValue = totalDeclareValue;
        }

        public Float getTotalWeight() {
            return totalWeight;
        }

        public void setTotalWeight(Float totalWeight) {
            this.totalWeight = totalWeight;
        }

        public Float getLength() {
            return length;
        }

        public void setLength(Float length) {
            this.length = length;
        }

        public Float getWidth() {
            return width;
        }

        public void setWidth(Float width) {
            this.width = width;
        }

        public Float getHeight() {
            return height;
        }

        public void setHeight(Float height) {
            this.height = height;
        }

        public String getRecipientName() {
            return recipientName;
        }

        public void setRecipientName(String recipientName) {
            this.recipientName = recipientName;
        }

        public String getHouseNumber() {
            return houseNumber;
        }

        public void setHouseNumber(String houseNumber) {
            this.houseNumber = houseNumber;
        }

        public String getTaxId() {
            return taxId;
        }

        public void setTaxId(String taxId) {
            this.taxId = taxId;
        }

        public String getRecipientAddress1() {
            return recipientAddress1;
        }

        public void setRecipientAddress1(String recipientAddress1) {
            this.recipientAddress1 = recipientAddress1;
        }

        public String getRecipientCountry() {
            return recipientCountry;
        }

        public void setRecipientCountry(String recipientCountry) {
            this.recipientCountry = recipientCountry;
        }

        public String getRecipientState() {
            return recipientState;
        }

        public void setRecipientState(String recipientState) {
            this.recipientState = recipientState;
        }

        public String getRecipientCity() {
            return recipientCity;
        }

        public void setRecipientCity(String recipientCity) {
            this.recipientCity = recipientCity;
        }

        public String getRecipientZip() {
            return recipientZip;
        }

        public void setRecipientZip(String recipientZip) {
            this.recipientZip = recipientZip;
        }

        public String getRecipientPhone() {
            return recipientPhone;
        }

        public void setRecipientPhone(String recipientPhone) {
            this.recipientPhone = recipientPhone;
        }

        public Item[] getItems() {
            return items;
        }

        public void setItems(Item[] items) {
            this.items = items;
        }

        public String getRecipientEmail() {
            return recipientEmail;
        }

        public void setRecipientEmail(String recipientEmail) {
            this.recipientEmail = recipientEmail;
        }
    }

    public Order[] getOrders() {
        return orders;
    }

    public void setOrders(Order[] orders) {
        this.orders = orders;
    }
}
