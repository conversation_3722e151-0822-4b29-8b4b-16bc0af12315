package com.nsy.api.tms.logistics.goodcang;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * HXD
 * 2022/7/11
 **/

public class VatChangeInfo {
    @JsonProperty(value = "ioss_number")
    private String iossNumber;
    @JsonProperty(value = "pid_number")
    private String pidNumber;
    @JsonProperty(value = "recipient_eori")
    private String recipientEori;
    @JsonProperty(value = "recipient_vat")
    private String recipientVat;
    @JsonProperty(value = "shipper_eori")
    private String shipperEori;
    @JsonProperty(value = "shipper_vat")
    private String shipperVat;

    public String getIossNumber() {
        return iossNumber;
    }

    public void setIossNumber(String iossNumber) {
        this.iossNumber = iossNumber;
    }

    public String getPidNumber() {
        return pidNumber;
    }

    public void setPidNumber(String pidNumber) {
        this.pidNumber = pidNumber;
    }

    public String getRecipientEori() {
        return recipientEori;
    }

    public void setRecipientEori(String recipientEori) {
        this.recipientEori = recipientEori;
    }

    public String getRecipientVat() {
        return recipientVat;
    }

    public void setRecipientVat(String recipientVat) {
        this.recipientVat = recipientVat;
    }

    public String getShipperEori() {
        return shipperEori;
    }

    public void setShipperEori(String shipperEori) {
        this.shipperEori = shipperEori;
    }

    public String getShipperVat() {
        return shipperVat;
    }

    public void setShipperVat(String shipperVat) {
        this.shipperVat = shipperVat;
    }
}
