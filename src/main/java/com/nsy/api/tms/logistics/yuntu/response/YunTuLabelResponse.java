package com.nsy.api.tms.logistics.yuntu.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class YunTuLabelResponse extends YunTuBaseResponse {

    @JsonProperty(value = "Item")
    public List<OrderLabelPrint> items; // 订单标签打印信息

    public static class OrderLabelPrint {
        @JsonProperty(value = "Url")
        public String url; // 打印标签地址

        @JsonProperty(value = "OrderInfos")
        public List<OrderInfo> orderInfos; // 订单详细信息

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public List<OrderInfo> getOrderInfos() {
            return orderInfos;
        }

        public void setOrderInfos(List<OrderInfo> orderInfos) {
            this.orderInfos = orderInfos;
        }
    }

    public static class OrderInfo {
        @JsonProperty(value = "CustomerOrderNumber")
        public String customerOrderNumber; // 客户订单号

        @JsonProperty(value = "Error")
        public String error; //  错误信息

        @JsonProperty(value = "Code")
        public int code; // 错误代码 100-正确，200-不存在打印模板

        public String getCustomerOrderNumber() {
            return customerOrderNumber;
        }

        public void setCustomerOrderNumber(String customerOrderNumber) {
            this.customerOrderNumber = customerOrderNumber;
        }

        public String getError() {
            return error;
        }

        public void setError(String error) {
            this.error = error;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }
    }

    public List<OrderLabelPrint> getItems() {
        return items;
    }

    public void setItems(List<OrderLabelPrint> items) {
        this.items = items;
    }
}
