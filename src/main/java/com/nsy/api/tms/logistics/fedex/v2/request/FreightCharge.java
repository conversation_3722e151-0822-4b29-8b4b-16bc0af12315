
package com.nsy.api.tms.logistics.fedex.v2.request;

import java.math.BigDecimal;

public class FreightCharge {

    private BigDecimal amount;
    private String currency;

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCurrency() {
        return currency;
    }

}
