package com.nsy.api.tms.logistics.diancang;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

/**
 * <AUTHOR>
 * @date 2024/4/7 10:39
 */
@XmlAccessorType(XmlAccessType.FIELD)
public class DianCangStockoutOrderDetail {

    /**
     * 订单中货物的总重量
     */
    @XmlElement(name = "GrossWeight")
    private Double grossWeight;

    /**
     * 尺寸(长,宽,高)，如1,2,3使用英文半角逗号分隔
     */
    @XmlElement(name = "Size")
    private String size;

    /**
     * 单位制类型：公制：Metric, 英制：Imperial
     */
    @XmlElement(name = "UnitType")
    private String unitType;

    /**
     * 订单备注信息
     */
    @XmlElement(name = "Remark")
    private Integer remark;

    /**
     * 订单状态
     */
    @XmlElement(name = "OrderStatus")
    private String orderStatus;

    public Double getGrossWeight() {
        return grossWeight;
    }

    public void setGrossWeight(Double grossWeight) {
        this.grossWeight = grossWeight;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getUnitType() {
        return unitType;
    }

    public void setUnitType(String unitType) {
        this.unitType = unitType;
    }

    public Integer getRemark() {
        return remark;
    }

    public void setRemark(Integer remark) {
        this.remark = remark;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }
}
