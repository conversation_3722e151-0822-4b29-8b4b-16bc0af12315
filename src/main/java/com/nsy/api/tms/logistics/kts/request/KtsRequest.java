package com.nsy.api.tms.logistics.kts.request;

import com.nsy.api.tms.request.BaseLogisticsOrderRequest;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlRootElement(name = "Request")
public class KtsRequest extends BaseLogisticsOrderRequest {

    @XmlAttribute(name = "service")
    private String service;

    @XmlAttribute(name = "lang")
    private String lang;

    @XmlElement(name = "Head")
    private String head;

    @XmlElement(name = "Body")
    private KtsRequestBody body;

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public String getHead() {
        return head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public KtsRequestBody getBody() {
        return body;
    }

    public void setBody(KtsRequestBody body) {
        this.body = body;
    }
}
