package com.nsy.api.tms.logistics.ups.ship.request;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @Author: <PERSON>
 * @Date: 2019/1/19 19:16
 */
public class Dimensions {
    @JsonProperty(value = "UnitOfMeasurement")
    private UnitOfMeasurement unitOfMeasurement;
    @JsonProperty(value = "Length")
    private String Length;
    @JsonProperty(value = "Width")
    private String width;
    @JsonProperty(value = "Height")
    private String height;

    public UnitOfMeasurement getUnitOfMeasurement() {
        return unitOfMeasurement;
    }

    public void setUnitOfMeasurement(UnitOfMeasurement unitOfMeasurement) {
        this.unitOfMeasurement = unitOfMeasurement;
    }

    public String getLength() {
        return Length;
    }

    public void setLength(String length) {
        Length = length;
    }

    public String getWidth() {
        return width;
    }

    public void setWidth(String width) {
        this.width = width;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }
}
