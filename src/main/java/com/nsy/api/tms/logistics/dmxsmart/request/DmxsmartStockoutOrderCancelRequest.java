package com.nsy.api.tms.logistics.dmxsmart.request;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * <AUTHOR>
 * @date 2024/7/31 17:44
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DmxsmartStockoutOrderCancelRequest {

    private String orderId;

    private String referenceId;

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }
}
