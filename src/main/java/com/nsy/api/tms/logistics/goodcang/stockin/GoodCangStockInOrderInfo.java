package com.nsy.api.tms.logistics.goodcang.stockin;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * HXD
 * 2022/8/26
 **/

public class GoodCangStockInOrderInfo {

    @JsonProperty(value = "receiving_code")
    private String receivingCode;
    @JsonProperty(value = "receiving_shipping_type")
    private Integer receivingShippingType;
    @JsonProperty(value = "transit_type")
    private Integer transitType;
    @JsonProperty(value = "sm_code")
    private Integer smCode;
    @JsonProperty(value = "reference_no")
    private String referenceNo;
    @JsonProperty(value = "receiving_status")
    private Integer receivingStatus;
    private BigDecimal weight;
    private BigDecimal volume;
    @JsonProperty(value = "receiving_desc")
    private String receivingDesc;
    @JsonProperty(value = "create_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createAt;
    @JsonProperty(value = "update_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateAt;
    @JsonProperty(value = "warehouse_code")
    private String warehouseCode;
    @JsonProperty(value = "box_total_count")
    private Integer boxTotalCount;
    @JsonProperty(value = "sku_total_count")
    private Integer skuTotalCount;
    @JsonProperty(value = "tracking_number")
    private String trackingNumber;
    @JsonProperty(value = "pickup_form")
    private Integer pickupForm;
    @JsonProperty(value = "clearance_service")
    private Integer clearanceService;
    @JsonProperty(value = "collecting_service")
    private Integer collectingService;
    @JsonProperty(value = "eta_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date etaDate;
    @JsonProperty(value = "gc_putaway_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gcPutawayTime;
    @JsonProperty(value = "overseas_detail")
    private List<OverseasDetail> overseasDetail;

    public static class OverseasDetail {

        @JsonProperty(value = "box_no")
        private String boxNo;
        @JsonProperty(value = "product_sku")
        private String productSku;
        @JsonProperty(value = "overseas_pre_count")
        private BigDecimal overseasPreCount;
        @JsonProperty(value = "overseas_receiving_count")
        private BigDecimal overseasReceivingCount;
        @JsonProperty(value = "overseas_shelves_count")
        private BigDecimal overseasShelvesCount;

        public BigDecimal getOverseasReceivingCount() {
            return overseasReceivingCount;
        }

        public void setOverseasReceivingCount(BigDecimal overseasReceivingCount) {
            this.overseasReceivingCount = overseasReceivingCount;
        }

        public String getBoxNo() {
            return boxNo;
        }

        public void setBoxNo(String boxNo) {
            this.boxNo = boxNo;
        }

        public String getProductSku() {
            return productSku;
        }

        public void setProductSku(String productSku) {
            this.productSku = productSku;
        }

        public BigDecimal getOverseasPreCount() {
            return overseasPreCount;
        }

        public void setOverseasPreCount(BigDecimal overseasPreCount) {
            this.overseasPreCount = overseasPreCount;
        }

        public BigDecimal getOverseasShelvesCount() {
            return overseasShelvesCount;
        }

        public void setOverseasShelvesCount(BigDecimal overseasShelvesCount) {
            this.overseasShelvesCount = overseasShelvesCount;
        }
    }

    public String getReceivingCode() {
        return receivingCode;
    }

    public void setReceivingCode(String receivingCode) {
        this.receivingCode = receivingCode;
    }

    public Integer getReceivingShippingType() {
        return receivingShippingType;
    }

    public void setReceivingShippingType(Integer receivingShippingType) {
        this.receivingShippingType = receivingShippingType;
    }

    public Integer getTransitType() {
        return transitType;
    }

    public void setTransitType(Integer transitType) {
        this.transitType = transitType;
    }

    public Integer getSmCode() {
        return smCode;
    }

    public void setSmCode(Integer smCode) {
        this.smCode = smCode;
    }

    public String getReferenceNo() {
        return referenceNo;
    }

    public void setReferenceNo(String referenceNo) {
        this.referenceNo = referenceNo;
    }

    public Integer getReceivingStatus() {
        return receivingStatus;
    }

    public void setReceivingStatus(Integer receivingStatus) {
        this.receivingStatus = receivingStatus;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getVolume() {
        return volume;
    }

    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }

    public String getReceivingDesc() {
        return receivingDesc;
    }

    public void setReceivingDesc(String receivingDesc) {
        this.receivingDesc = receivingDesc;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Date getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Date updateAt) {
        this.updateAt = updateAt;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public Integer getBoxTotalCount() {
        return boxTotalCount;
    }

    public void setBoxTotalCount(Integer boxTotalCount) {
        this.boxTotalCount = boxTotalCount;
    }

    public Integer getSkuTotalCount() {
        return skuTotalCount;
    }

    public void setSkuTotalCount(Integer skuTotalCount) {
        this.skuTotalCount = skuTotalCount;
    }

    public String getTrackingNumber() {
        return trackingNumber;
    }

    public void setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber;
    }

    public Integer getPickupForm() {
        return pickupForm;
    }

    public void setPickupForm(Integer pickupForm) {
        this.pickupForm = pickupForm;
    }

    public Integer getClearanceService() {
        return clearanceService;
    }

    public void setClearanceService(Integer clearanceService) {
        this.clearanceService = clearanceService;
    }

    public Integer getCollectingService() {
        return collectingService;
    }

    public void setCollectingService(Integer collectingService) {
        this.collectingService = collectingService;
    }

    public Date getEtaDate() {
        return etaDate;
    }

    public void setEtaDate(Date etaDate) {
        this.etaDate = etaDate;
    }

    public Date getGcPutawayTime() {
        return gcPutawayTime;
    }

    public void setGcPutawayTime(Date gcPutawayTime) {
        this.gcPutawayTime = gcPutawayTime;
    }

    public List<OverseasDetail> getOverseasDetail() {
        return overseasDetail;
    }

    public void setOverseasDetail(List<OverseasDetail> overseasDetail) {
        this.overseasDetail = overseasDetail;
    }
}
