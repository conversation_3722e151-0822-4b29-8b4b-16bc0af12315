
package com.nsy.api.tms.logistics.cangsou;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebService(name = "Ec", targetNamespace = "http://www.example.org/Ec/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface Ec {


    /**
     * 
     * @param paramsJson
     * @param appToken
     * @param service
     * @param appKey
     * @return
     *     returns java.lang.String
     */
    @WebMethod(action = "http://www.example.org/Ec/callService")
    @WebResult(name = "response", targetNamespace = "")
    @RequestWrapper(localName = "callService", targetNamespace = "http://www.example.org/Ec/", className = "com.nsy.api.tms.logistics.cangsou.CallService")
    @ResponseWrapper(localName = "callServiceResponse", targetNamespace = "http://www.example.org/Ec/", className = "com.nsy.api.tms.logistics.cangsou.CallServiceResponse")
    public String callService(
        @WebParam(name = "paramsJson", targetNamespace = "")
        String paramsJson,
        @WebParam(name = "appToken", targetNamespace = "")
        String appToken,
        @WebParam(name = "appKey", targetNamespace = "")
        String appKey,
        @WebParam(name = "service", targetNamespace = "")
        String service);

}
