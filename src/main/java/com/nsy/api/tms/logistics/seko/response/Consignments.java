
package com.nsy.api.tms.logistics.seko.response;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class Consignments {

    @JsonProperty(value = "Connote")
    private String connote;
    @JsonProperty(value = "TrackingUrl")
    private String trackingUrl;
    @JsonProperty(value = "Cost")
    private Integer cost;
    @JsonProperty(value = "CarrierType")
    private Integer carrierType;
    @JsonProperty(value = "IsSaturdayDelivery")
    private Boolean isSaturdayDelivery;
    @JsonProperty(value = "IsRural")
    private Boolean isRural;
    @JsonProperty(value = "IsOvernight")
    private Boolean isOvernight;
    @JsonProperty(value = "HasTrackPaks")
    private Boolean hasTrackPaks;
    @JsonProperty(value = "ConsignmentId")
    private Long consignmentId;
    @JsonProperty(value = "OutputFiles")
    private OutputFiles outputFiles;
    @JsonProperty(value = "Items")
    private List<Items> items;

    public String getConnote() {
        return connote;
    }

    public void setConnote(String connote) {
        this.connote = connote;
    }

    public String getTrackingUrl() {
        return trackingUrl;
    }

    public void setTrackingUrl(String trackingUrl) {
        this.trackingUrl = trackingUrl;
    }

    public Integer getCost() {
        return cost;
    }

    public void setCost(Integer cost) {
        this.cost = cost;
    }

    public Integer getCarrierType() {
        return carrierType;
    }

    public void setCarrierType(Integer carrierType) {
        this.carrierType = carrierType;
    }

    public Boolean getSaturdayDelivery() {
        return isSaturdayDelivery;
    }

    public void setSaturdayDelivery(Boolean saturdayDelivery) {
        isSaturdayDelivery = saturdayDelivery;
    }

    public Boolean getRural() {
        return isRural;
    }

    public void setRural(Boolean rural) {
        isRural = rural;
    }

    public Boolean getOvernight() {
        return isOvernight;
    }

    public void setOvernight(Boolean overnight) {
        isOvernight = overnight;
    }

    public Boolean getHasTrackPaks() {
        return hasTrackPaks;
    }

    public void setHasTrackPaks(Boolean hasTrackPaks) {
        this.hasTrackPaks = hasTrackPaks;
    }

    public Long getConsignmentId() {
        return consignmentId;
    }

    public void setConsignmentId(Long consignmentId) {
        this.consignmentId = consignmentId;
    }

    public OutputFiles getOutputFiles() {
        return outputFiles;
    }

    public void setOutputFiles(OutputFiles outputFiles) {
        this.outputFiles = outputFiles;
    }

    public List<Items> getItems() {
        return items;
    }

    public void setItems(List<Items> items) {
        this.items = items;
    }
}
