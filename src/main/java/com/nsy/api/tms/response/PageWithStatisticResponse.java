package com.nsy.api.tms.response;

public class PageWithStatisticResponse<T, E> extends PageResponse<T> {
    private E statisticSummary;

    public static <T, E> PageWithStatisticResponse<T, E> of(Long totalCount, Integer number, E statisticSummary) {
        PageWithStatisticResponse<T, E> pageWithStatisticResponse = new PageWithStatisticResponse<>();
        pageWithStatisticResponse.setTotalCount(totalCount);
        pageWithStatisticResponse.setNumber(number);
        pageWithStatisticResponse.setStatisticSummary(statisticSummary);
        return pageWithStatisticResponse;
    }

    public E getStatisticSummary() {
        return statisticSummary;
    }

    public void setStatisticSummary(E statisticSummary) {
        this.statisticSummary = statisticSummary;
    }
}
