package com.nsy.api.tms.response.download;

import io.swagger.annotations.ApiModelProperty;

public class TmsLogisticsCompanyExportResponse {

    @ApiModelProperty(value = "公司名称", name = "logisticsCompany")
    private String logisticsCompany;

    @ApiModelProperty(value = "公司代码", name = "logisticsCode")
    private String logisticsCode;

    @ApiModelProperty(value = "物流类型", name = "logisticsType")
    private String logisticsType;

    @ApiModelProperty(value = "优先级", name = "priority")
    private Integer priority;

    @ApiModelProperty(value = "地区", name = "location")
    private String location;

    @ApiModelProperty(value = "工作区域", name = "workspace")
    private String workspace;

    @ApiModelProperty(value = "是否启用:1--启用 0--停用", name = "statusStr")
    private String statusStr;

    @ApiModelProperty(value = "打印规格", name = "printSpec")
    private String printSpec;

    @ApiModelProperty(value = "分销可用 1-是，0-否", name = "distributionAvailableStr")
    private String distributionAvailableStr;

    @ApiModelProperty(value = "物流查询显示 1-是，0-否", name = "queryDisplayStr")
    private String queryDisplayStr;

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getLogisticsCode() {
        return logisticsCode;
    }

    public void setLogisticsCode(String logisticsCode) {
        this.logisticsCode = logisticsCode;
    }

    public String getLogisticsType() {
        return logisticsType;
    }

    public void setLogisticsType(String logisticsType) {
        this.logisticsType = logisticsType;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getWorkspace() {
        return workspace;
    }

    public void setWorkspace(String workspace) {
        this.workspace = workspace;
    }

    public String getPrintSpec() {
        return printSpec;
    }

    public void setPrintSpec(String printSpec) {
        this.printSpec = printSpec;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public String getDistributionAvailableStr() {
        return distributionAvailableStr;
    }

    public void setDistributionAvailableStr(String distributionAvailableStr) {
        this.distributionAvailableStr = distributionAvailableStr;
    }

    public String getQueryDisplayStr() {
        return queryDisplayStr;
    }

    public void setQueryDisplayStr(String queryDisplayStr) {
        this.queryDisplayStr = queryDisplayStr;
    }
}
