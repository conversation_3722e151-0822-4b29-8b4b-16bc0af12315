package com.nsy.api.tms.response.base;

import java.util.List;

/**
 * User: Emily
 * Date: 2018/4/25
 * 说明：用于分页查询
 */
public class BaseListResponse<T> {
    private long totalCount;
    private List<T> content;

    public BaseListResponse() {
    }

    public BaseListResponse(long totalCount, List<T> content) {
        this.totalCount = totalCount;
        this.content = content;
    }

    public long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(long totalCount) {
        this.totalCount = totalCount;
    }

    public List<T> getContent() {
        return content;
    }

    public void setContent(List<T> content) {
        this.content = content;
    }
}
