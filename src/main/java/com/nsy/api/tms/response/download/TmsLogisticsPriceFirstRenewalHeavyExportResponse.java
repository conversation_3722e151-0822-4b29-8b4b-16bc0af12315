package com.nsy.api.tms.response.download;

import java.math.BigDecimal;

public class TmsLogisticsPriceFirstRenewalHeavyExportResponse {

    // 物流公司
    private String logisticsCompany;

    // 物流渠道
    private String logisticsChannelName;

    // 国家
    private String countries;

    // 挂号费用
    private BigDecimal registrationFee;

    // 续重费用
    private BigDecimal renewalFee;

    // 折扣
    private BigDecimal discount;

    // 时效
    private Integer aging;

    // 区间重量
    private String intervalWeight;

    // 区间费用
    private String intervalFee;

    public String getLogisticsCompany() {
        return logisticsCompany;
    }

    public void setLogisticsCompany(String logisticsCompany) {
        this.logisticsCompany = logisticsCompany;
    }

    public String getLogisticsChannelName() {
        return logisticsChannelName;
    }

    public void setLogisticsChannelName(String logisticsChannelName) {
        this.logisticsChannelName = logisticsChannelName;
    }

    public String getCountries() {
        return countries;
    }

    public void setCountries(String countries) {
        this.countries = countries;
    }

    public BigDecimal getRegistrationFee() {
        return registrationFee;
    }

    public void setRegistrationFee(BigDecimal registrationFee) {
        this.registrationFee = registrationFee;
    }

    public BigDecimal getRenewalFee() {
        return renewalFee;
    }

    public void setRenewalFee(BigDecimal renewalFee) {
        this.renewalFee = renewalFee;
    }

    public BigDecimal getDiscount() {
        return discount;
    }

    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }

    public Integer getAging() {
        return aging;
    }

    public void setAging(Integer aging) {
        this.aging = aging;
    }

    public String getIntervalWeight() {
        return intervalWeight;
    }

    public void setIntervalWeight(String intervalWeight) {
        this.intervalWeight = intervalWeight;
    }

    public String getIntervalFee() {
        return intervalFee;
    }

    public void setIntervalFee(String intervalFee) {
        this.intervalFee = intervalFee;
    }
}
