package com.nsy.api.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nsy.api.tms.dao.entity.ForwarderPriceEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 头程运价业务报价 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-18
 */
@Mapper
public interface ForwarderPriceMapper extends BaseMapper<ForwarderPriceEntity> {

    List<String> getFreightModesByLocation(@Param("location") String location);

}
