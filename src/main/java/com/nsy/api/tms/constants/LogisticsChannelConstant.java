package com.nsy.api.tms.constants;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 需要预处理的  渠道
 * <AUTHOR>
 * 2023-08-26
 */
public class LogisticsChannelConstant {

    public static final String YOU_SU = "优速快递";
    public static final String YUAN_TONG = "圆通快递";
    public static final String SU_ER = "速尔快递";
    public static final String JTSD = "极兔速递";
    public static final String YUN_DA = "韵达快递";
    public static final String SF = "顺丰";
    public static final String EMS = "EMS";
    public static final String ZTO = "中通快递";
    public static final String KUA_YUE_SU_YUN = "跨越速运";

    public static final String EDL_EXPRESS = "EdlExpress";
    public static final String EDL_EXPRESS_W = "EdlExpress-W";
    public static final String USPS_US = "USPS-US";
    public static final String EDL_HUAMEI = "Edl-华美实力派";
    public static final String EDL_ESUBAO = "Edl-E速宝小包";
    public static final String EDL_WNBAA_XIAOBAO = "Edl_WNBAA";

    public static final String EMSGJ = "EMS国际";
    public static final String E_YOU_BAO = "E邮宝";
    public static final String E_TE_KUAI = "E邮宝-特快";

    public static final String FOUR_PX = "4px-联邮通"; //联邮通标准挂号-带电
    public static final String FOUR_PX_MEIPU = "联邮通美国普货专线"; //联邮通标准挂号-普货
    public static final String XINJIAPO_XIAOBAO = "新加坡小包";
    public static final String FOUR_PX_DHL = "4PX-DHL";
    public static final String FOUR_PX_FANGYI = "4PX-泡货";

    public static final String GUANG_SU = "光速全美特惠";
    public static final String JETPLUS_USPS_XUNI = "USPS全程(虚拟仓)";
    public static final String JETPLUS_USPS_XUNI_ZHUANXIAN = "USPS全程(虚拟仓)专线";
    public static final String JETPLUS_USPS_KONGPAI_ZHUANXIAN = "USPS全程(空派)专线 -非预上网";

    public static final String JINMAI_US_PUHUO = "金麦-美国普货小包";
    public static final String JIACHENG_US_VIRTUAL_AREHOUSE = "JC（金麦）-美国虚拟仓";

    public static final String DA_LU_DHL = "大陆DHL(小包)";
    public static final String GUANG_ZHOU_EUB = "广州EUB";
    public static final String QUANQIU_PUHUO = "全球专线挂号-普货";
    public static final String JY_EMS_ZHUANXIAN = "EMS 专线小包";
    public static final String US_TEKUAI_ZHUANXIAN = "美国特快专线小包";

    public static final String SF_EUROPE = "顺丰-欧洲小包";
    public static final String SF_CD_GUANGZHOU = "广州顺丰CD";
    public static final String SF_FANHAI = "SY-FH美国专线";
    public static final String SF_F2 = "顺丰国际-F2";
    public static final String SF_F2_PREPAY = "顺丰-F2预付";
    public static final String SF_PREPAY = "顺丰-日韩预付小包";

    public static final String YOUZHENGXIAOBAO_EYOUBAO = "邮政小包-E邮宝";
    public static final String YOUZHENGXIAOBAO_ETEKUAI = "邮政小包-E特快";
    public static final String YOUZHENGXIAOBAO_EMS = "邮政小包-EMS";

    public static final String YUN_TU = "云途特惠普货";
    public static final String YUN_TU_BKPHR = "云途-标快(Walmart)";
    public static final String DE_QI = "德启";
    public static final String CNE_EXPRESS = "CneExpress";
    public static final String CHENGDU_EYOUBAO = "成都E邮宝";
    public static final String CANG_SOU_UPS_MI = "仓搜_UPS_MI";
    public static final String CANG_SOU = "仓搜";
    public static final String SE_KO = "Seko";
    public static final String YI_KE_DA = "易可达";
    public static final String YA_XIANG_GUO_JI = "亚翔国际";
    public static final String CAI_NIAO_WU_YOU = "邮政小包-无忧";
    public static final String XING_SHUN_YI = "星顺奕";
    public static final String SU_DI_GUAN_JIA = "速递管家";
    public static final String TIAN_DI_ZONG_HENG = "天地纵横";
    public static final String YDH = "义速宝全球快捷(普货)";
    public static final String YUAN_FEI_HANG = "飞航-BKJPHN";
    public static final String XING_YU_LONG = "鑫宇隆";
    public static final String JIA_CHENG = "佳成";
    public static final String DHL_B2C = "DHL-B2C";
    public static final String FEDEX = "FedEx";
    public static final String DHL = "DHL";
    public static final String UPS = "UPS";
    public static final String UPS_HZ = "UPS 杭州";
    public static final String UPS_FAST = "UPS 速快";
    public static final String UPS_QUICK = "UPS 快捷";
    public static final String GOODCANG_USSC_UPS = "谷仓-美南UPS";
    public static final String ZHONG_CHENG_HANG_USPS_TP = "中成航-美国专线";
    public static final String UPS_EC = "UPS经济";
    public static final String YAN_WEN = "沃尔玛燕文专线快递";
    public static final String SAI_YA = "sy速递管家";
    public static final String AFM = "AFM海外仓";

    public static final String GOODCANG_USPS_QZ = "谷仓-USPS(泉州)";
    public static final String GOODCANG_GC_PARCEL = "谷仓-GC-Parcel(泉州)";
    public static final String GOODCANG_UPS_QZ = "谷仓-UPS(泉州)";

    // ---------厦门公司------------
    public static final String DHL_XM = "DHL(厦门)";
    public static final String FEDEX_XM = "FedEx(厦门)";
    public static final String UPS_XM = "UPS(厦门)";
    public static final String EUB_XM = "国际e邮宝(厦门)";
    public static final String FOURPX_OH_XM = "联邮通标准挂号-带电OH(厦门)";
    public static final String FOURPX_QC_P_XM = "全球速递标准-QY(厦门)";
    public static final String FOURPX_F4_XM = "4PX-华南小包挂号(厦门)";
    public static final String FOURPX_PX_XM = "联邮通优先挂号PX(厦门)";
    public static final String FOURPX_QZ_XM = "全球速递标准带电QZ(厦门)";
    public static final String FOURPX_QC_XM = "联邮通标准挂号QC(厦门)";
    public static final String FOURPX_S945_XM = "联邮通冰爽价-普货S945(厦门)";
    public static final String FOURPX_S1427_XM = "4PX联邮通服装专线(厦门)";
    public static final String YUNTU_THZXR_XM = "云途全球专线挂号-特惠普货(THPHR)(厦门)";
    public static final String YUNTU_THPHR_XM = "云途全球专线带电-THPHR(厦门)";
    public static final String YUNTU_FZZXR_XM = "云途全球服装专线-FZXR(厦门)";
    public static final String YUNTU_BKPHR_XM = "云途全球专线挂号(标快普货)-BKPHR(厦门)";
    public static final String SFXB_XM = "顺丰国际电商专递-CD(厦门)";
    public static final String EDL_HUAMEI_XM = "EDL(厦门)";
    public static final String EDL_WNBAA_XM = "EDL-WanbExpress(厦门)";
    public static final String DHL_B2C_XM = "DHL-B2C(XM)";
    // ---------厦门公司 END------------

    // ---------广州公司------------
    public static final String FOURPX_S945_GUANGZHOU_QZ = "总部-广州联邮通特惠普货";
    public static final String FOURPX_MEIPU_QC_GUANGZHOU_QZ = "总部-广州联邮通普货专线";
    public static final String FOURPX_S945_GUANGZHOU = "广州联邮通特惠专线";
    public static final String FOURPX_MEIPU_GUANGZHOU = "广州-联邮通普货专线";
    public static final String FOURPX_QY_GUANGZHOU = "广州-联邮通加拿大专线";
    public static final String FOURPX_CA_QY_GUANGZHOU_QZ = "总部-广州递四方-加拿大专线";
    public static final String DHL_B2C_GUANGZHOU = "DHL-B2C(ZB)";
    public static final String YUNTU_THZXR_GZ = "云途特惠普货(广州)";
    public static final String YUNTU_BKPHR_GZ = "云途沃尔玛专线(广州)";
    public static final String YANWEN_GZ = "沃尔玛燕文专线-广州普货";
    // ---------广州公司 END------------


    // 宓思
    public static final String FOURPX_EC_MISI = "宓思广州E邮宝";

    public static final List<String> EDL_LOGISTICS_COMPANY = Collections.unmodifiableList(Arrays.asList(EDL_EXPRESS, EDL_EXPRESS_W, USPS_US, EDL_HUAMEI, EDL_HUAMEI_XM, EDL_ESUBAO, EDL_WNBAA_XIAOBAO, EDL_WNBAA_XM));
    public static final List<String> YUNTU_LOGISTICS_COMPANY = Collections.unmodifiableList(Arrays.asList(YUN_TU, YUNTU_FZZXR_XM, YUNTU_THPHR_XM, YUNTU_THZXR_XM, YUN_TU_BKPHR, YUNTU_BKPHR_XM, YUNTU_THZXR_GZ, YUNTU_BKPHR_GZ));
    public static final List<String> ZHONGCHEGNHANG_LOGISTICS_COMPANY = Collections.unmodifiableList(Collections.singletonList(ZHONG_CHENG_HANG_USPS_TP));

    public static final List<String> FOURPX_LOGISTICS_COMPANY = Collections.unmodifiableList(Arrays.asList(FOURPX_S945_GUANGZHOU,
            FOURPX_MEIPU_GUANGZHOU, FOURPX_QY_GUANGZHOU, FOURPX_OH_XM, FOURPX_QC_P_XM, FOURPX_PX_XM, FOURPX_QZ_XM, FOURPX_QC_XM,
            FOURPX_S945_XM, FOUR_PX, FOUR_PX_MEIPU, FOURPX_F4_XM, FOURPX_S1427_XM));


}
