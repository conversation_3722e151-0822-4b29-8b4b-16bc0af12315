package com.nsy.api.tms.service.external.refactor;

import com.nsy.api.tms.annotation.LogisticsServiceHandler;
import com.nsy.api.tms.domain.OrderItemInfo;
import com.nsy.api.tms.enumeration.LogisticsMethodEnum;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@LogisticsServiceHandler(logisticsMethod = LogisticsMethodEnum.JinMai)
public class JinMaiNewService extends HuaLeiNewService implements InitializingBean {

    @Value("${jinmai.createorder.url}")
    private String jinMaiCreateOrderUrl;
    @Value("${jinmai.printLabel.url}")
    private String jinMaiPrintLabelUrl;
    @Value("${jinmai.ip.url}")
    private String jinMaiHostIP;
    @Value("${jinmai.authentication.url}")
    private String jinMaiAuthenticationUrl;
    @Value("${jinmai.track.url}")
    private String jinMaiTrackUrl;
    @Value("${jinmai.trackNumber.url}")
    String jinMaiTrackingNumberUrl;

    @Override
    public void afterPropertiesSet() {
        this.createOrderUrl = jinMaiCreateOrderUrl;
        this.printLabelUrl = jinMaiPrintLabelUrl;
        this.hostIP = jinMaiHostIP;
        this.authenticationUrl = jinMaiAuthenticationUrl;
        this.trackUrl = jinMaiTrackUrl;
        this.trackingNumberUrl = jinMaiTrackingNumberUrl;
        this.labelFolder += "JinMai/";
        this.ossLabelFolder += "JinMai/";
        this.labelNameTemplate = "%sJinMai-%s";
    }

    /**
     * 佳成物流 要求 面单品名为中英文
     * <AUTHOR>
     * 2021-05-31
     */
    @Override
    protected String getSkuByByService(OrderItemInfo orderItem, String channelCode) {
        return orderItem.getCnName();
    }
}
