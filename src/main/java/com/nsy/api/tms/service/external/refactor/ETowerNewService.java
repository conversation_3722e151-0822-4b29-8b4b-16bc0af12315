package com.nsy.api.tms.service.external.refactor;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.tms.annotation.LogisticsServiceHandler;
import com.nsy.api.tms.dao.entity.TmsLogisticsAccountEntity;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.domain.Address;
import com.nsy.api.tms.domain.OrderItemInfo;
import com.nsy.api.tms.domain.order.OrderNewInfo;
import com.nsy.api.tms.enumeration.LogisticsMethodEnum;
import com.nsy.api.tms.logistics.etower.request.ETowerCreateExtendDataInfo;
import com.nsy.api.tms.logistics.etower.request.ETowerCreateOrderItemInfo;
import com.nsy.api.tms.logistics.etower.request.ETowerCreateOrderRequest;
import com.nsy.api.tms.logistics.etower.request.ETowerPrintLabelRequest;
import com.nsy.api.tms.logistics.etower.response.ETowerCreateOrderResponse;
import com.nsy.api.tms.logistics.etower.response.ETowerPrintLabelResponse;
import com.nsy.api.tms.request.OrderNewRequest;
import com.nsy.api.tms.response.GenerateOrderResponse;
import com.nsy.api.tms.response.PrintLabelResponse;
import com.nsy.api.tms.utils.JsonMapper;
import com.sun.jersey.core.util.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @date 2024/4/15 9:49
 */
@Service
@LogisticsServiceHandler(logisticsMethod = LogisticsMethodEnum.ETOWER)
public class ETowerNewService extends BaseLogisticsNewService implements InitializingBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(ETowerNewService.class);

    private static final char NEW_LINE = (char) 0x000A;

    private static final String HEADERS_AUTHORIZATION_PREFIX = "WallTech ";

    private static final String HMAC_SHA1_ALGORITHM = "HmacSHA1";

    public static final String APP_TOKEN = "e-tower-token";

    public static final String APP_SECRET = "e-tower-secret";

    public static final String LABEL_TYPE = "e-tower-label-type";

    public static final String APP_SERVICE_CODE = "e-tower-service-code";

    //au场景使用
    public static final String APP_SERVICE_CODE_AU = "e-tower-service-code-au";

    //GB场景使用
    public static final String APP_SERVICE_CODE_GB = "e-tower-service-code-gb";

    //US场景使用
    public static final String APP_SERVICE_CODE_US = "e-tower-service-code-us";

    private static final String LABEL_NAME = "%s-e-tower-%s";

    @Value("${eTower.server.url}")
    private String eTowerUrl;

    @Override
    public void afterPropertiesSet() throws Exception {

    }

    /**
     * 请求下订单
     */
    @Override
    public GenerateOrderResponse doRequest(OrderNewRequest orderRequest, Map<String, String> configMap, String requestContent) {
        GenerateOrderResponse shipperOrder = this.createShipperOrder(orderRequest, configMap, requestContent);
        //由于AU的UBI.WW2AU.ARAMEX.GENERAL 因为这个服务不是全境覆盖 ，故UBI.WW2AU.ARAMEX.GENERAL 不支持派送的地区 就必须用UBI.CN2WW.GENERAL 仅限AU地区
        //当前处理方式如果AU地区的调用错误，系统自动帮忙更换渠道再调用一次，如果还是错误需要则为更换渠道无法解决报错，需要业务处理
        if ("AU".equalsIgnoreCase(orderRequest.getOrderInfo().getReceiver().getCountry())
                && !ObjectUtils.isEmpty(shipperOrder.getError()) && !StringUtils.isBlank(shipperOrder.getError().getMessage())) {
            List<ETowerCreateOrderRequest> eTowerCreateOrderList = JsonMapper.jsonStringToObjectArray(requestContent, ETowerCreateOrderRequest.class);
            eTowerCreateOrderList.get(0).setServiceCode(configMap.get(APP_SERVICE_CODE));
            return this.createShipperOrder(orderRequest, configMap, JsonMapper.toJson(eTowerCreateOrderList));
        }
        return shipperOrder;
    }

    private GenerateOrderResponse createShipperOrder(OrderNewRequest orderRequest, Map<String, String> configMap, String requestContent) {
        TmsRequestLogEntity logEntity = requestLogService.recordBaseInfoLog(orderRequest.getOrderInfo());
        GenerateOrderResponse response = new GenerateOrderResponse();
        LOGGER.debug("请求参数:{}", requestContent);
        //invoke api
        try {
            ETowerCreateOrderResponse eTowerResponse = this.postToETower(requestContent, configMap, "/services/shipper/orders", ETowerCreateOrderResponse.class);
            LOGGER.info("e-tower请求订单响应:{}", ObjectUtils.isEmpty(eTowerResponse) ? "请求返回为空" : JsonMapper.toJson(eTowerResponse));
            if (ObjectUtils.isEmpty(eTowerResponse) || !"Success".equalsIgnoreCase(eTowerResponse.getStatus())) {
                LOGGER.error("e-tower创建订单失败：request=={}==， response=={}==", requestContent, JsonMapper.toJson(eTowerResponse == null ? "服务器错误！" : eTowerResponse));
                requestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, requestContent, ObjectUtils.isEmpty(eTowerResponse) ? "请求返回为空" : JsonMapper.toJson(eTowerResponse), null);
                response.setError(buildError("400", ObjectUtils.isEmpty(eTowerResponse) ? "请求返回为空" : eTowerResponse.getErrors().get(0).getMessage()));
            } else {
                GenerateOrderResponse.SuccessEntity successEntity = buildSuccessEntity(orderRequest.getOrderInfo(), eTowerResponse.getData().get(0).getTrackingNo(), "", orderRequest.getOrderInfo().getTid(), eTowerResponse.getData().get(0).getOrderId());
                response.setSuccessEntity(successEntity);
                requestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, requestContent, JsonMapper.toJson(eTowerResponse), successEntity.getLogisticsNo());
            }
        } catch (Exception e) {
            requestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, requestContent, e.getMessage(), null);
            throw new RuntimeException(e);
        }
        return response;
    }

    @Override
    protected String buildSerializedRequest(OrderNewRequest request, TmsLogisticsAccountEntity logisticsAccountEntity) {
        Map<String, String> configMap = buildAccountConfig(logisticsAccountEntity, request.getOrderInfo());
        ETowerCreateOrderRequest request1 = buildLogisticsOrderRequest(request, configMap);
        return JsonMapper.toJson(Collections.singletonList(request1));
    }

    /**
     * 打印面单
     */
    @Override
    public PrintLabelResponse printLabel(TmsPackageEntity packageEntity) {
        if (!StringUtils.isBlank(packageEntity.getLabelUrl())) {
            return super.printLabel(packageEntity);
        }
        Map<String, String> configMap = logisticsAccountService.buildAccountConfig(packageEntity.getLogisticsAccountId());
        PrintLabelResponse response = new PrintLabelResponse();
        String labelUrl = getLabel(configMap, packageEntity.getSecondaryNumber());
        if (!StringUtils.isBlank(labelUrl)) {
            packageEntity.setLabelUrl(labelUrl);
            packageService.save(packageEntity);
            response.setLabelUrl(labelUrl);
            response.setLogisticsNo(packageEntity.getLogisticsNo());
            response.setTmsTid(packageEntity.getTmsTid());
            response.setTid(packageEntity.getTid());
            response.setBusinessKey(packageEntity.getBusinessKey());
        }
        return response;
    }

    @Override
    public void syncDeliveryInfo(TmsPackageEntity packageEntity) {
        Map<String, String> configMap = logisticsAccountService.buildAccountConfig(packageEntity.getLogisticsAccountId());
        //发货货物清单
        this.postToETower(JsonMapper.toJson(Collections.singletonList(packageEntity.getSecondaryNumber())), configMap, "/services/shipper/manifests", String.class);
    }

    private String getLabel(Map<String, String> configMap, String trackingNumber) {
        ETowerPrintLabelRequest eTowerPrintLabelRequest = new ETowerPrintLabelRequest();
        eTowerPrintLabelRequest.setOrderIds(Collections.singletonList(trackingNumber));
        eTowerPrintLabelRequest.setLabelType(configMap.get(LABEL_TYPE));
        eTowerPrintLabelRequest.setMerged(Boolean.TRUE);
        ETowerPrintLabelResponse eTowerResponse = this.postToETower(JsonMapper.toJson(eTowerPrintLabelRequest), configMap, "/services/shipper/labels", ETowerPrintLabelResponse.class);
        if (ObjectUtils.isEmpty(eTowerResponse) || !"Success".equalsIgnoreCase(eTowerResponse.getStatus()) || CollectionUtils.isEmpty(eTowerResponse.getData())) {
            LOGGER.error("e-tower获取打印信息失败：request=={}==， response=={}==", trackingNumber, JsonMapper.toJson(eTowerResponse == null ? "服务器错误！" : eTowerResponse));
            return null;
        }
        return this.downloadLabelPdfAndUploadOSS(eTowerResponse.getData().get(0).getLabelContent(), trackingNumber);
    }

    /**
     *
     */
    @Override
    protected ETowerCreateOrderRequest buildLogisticsOrderRequest(OrderNewRequest orderRequest, Map<String, String> configMap) {
        ETowerCreateOrderRequest tomsOrderRequest = new ETowerCreateOrderRequest();
        OrderNewInfo orderInfo = orderRequest.getOrderInfo();
        tomsOrderRequest.setReferenceNo(orderInfo.getBusinessKey());
        tomsOrderRequest.setReferenceNo1(orderInfo.getTid());
        this.buildAddressInfo(orderInfo, tomsOrderRequest);
        //AU且收件人地址不为po box地址
        if ("AU".equalsIgnoreCase(orderInfo.getReceiver().getCountry()) && !tomsOrderRequest.getAddressLine1().contains("P.O. Box")) {
            tomsOrderRequest.setServiceCode(configMap.get(APP_SERVICE_CODE_AU));
        } else if ("US".equalsIgnoreCase(orderInfo.getReceiver().getCountry())) {
            tomsOrderRequest.setServiceCode(configMap.get(APP_SERVICE_CODE_US));
        } else if ("GB".equalsIgnoreCase(orderInfo.getReceiver().getCountry())) {
            tomsOrderRequest.setServiceCode(configMap.get(APP_SERVICE_CODE_GB));
        } else {
            tomsOrderRequest.setServiceCode(configMap.get(APP_SERVICE_CODE));
        }
        if (StringUtils.isNotBlank(orderInfo.getIossNumber())) {
            ETowerCreateExtendDataInfo extendDataInfo = new ETowerCreateExtendDataInfo();
            extendDataInfo.setVendorid(orderInfo.getIossNumber());
            tomsOrderRequest.setExtendData(extendDataInfo);
        }
        tomsOrderRequest.setInvoiceCurrency(StringUtils.isNotBlank(orderInfo.getIossNumber()) ? "EUR" : "USD");
        tomsOrderRequest.setInvoiceValue(orderInfo.getCustomsValueAmount());
        tomsOrderRequest.setWeightUnit("KG");
        tomsOrderRequest.setWeight(orderInfo.getWeight());
        this.buildItemInfo(tomsOrderRequest, orderInfo.getOrderItemInfoList());
        return tomsOrderRequest;
    }

    private void buildAddressInfo(OrderNewInfo orderInfo, ETowerCreateOrderRequest tomsOrderRequest) {
        //收件人信息
        Address receiver = orderInfo.getReceiver();
        tomsOrderRequest.setCountry(receiver.getCountry());
        tomsOrderRequest.setState(receiver.getProvince());
        tomsOrderRequest.setCity(receiver.getCity());
        tomsOrderRequest.setPostcode(receiver.getPostCode());
        tomsOrderRequest.setRecipientName(receiver.getName());
        tomsOrderRequest.setPhone(receiver.getPhone());
        tomsOrderRequest.setEmail(receiver.getEmail());
        if (receiver.getStreet().length() <= 200) {
            tomsOrderRequest.setAddressLine1(StringUtils.substring(receiver.getStreet(), 0, 200));
        } else if (receiver.getStreet().length() > 200 && receiver.getStreet().length() <= 400) {
            tomsOrderRequest.setAddressLine1(StringUtils.substring(receiver.getStreet(), 0, 200));
            tomsOrderRequest.setAddressLine2(StringUtils.substring(receiver.getStreet(), 200, 400));
        } else if (receiver.getStreet().length() > 400 && orderInfo.getReceiver().getStreet().length() <= 480) {
            tomsOrderRequest.setAddressLine1(StringUtils.substring(receiver.getStreet(), 0, 200));
            tomsOrderRequest.setAddressLine2(StringUtils.substring(receiver.getStreet(), 200, 400));
            tomsOrderRequest.setAddressLine3(StringUtils.substring(receiver.getStreet(), 400, 80));
        } else if (orderInfo.getReceiver().getStreet().length() > 480) {
            throw new BusinessServiceException("收件人地址不能大于480个字符");
        }
        //发件人信息
        Address sender = orderInfo.getSender();
        tomsOrderRequest.setShipperName(sender.getName());
        tomsOrderRequest.setShipperCity(sender.getCity());
        tomsOrderRequest.setShipperPhone(sender.getPhone());
        tomsOrderRequest.setShipperState(sender.getProvince());
        tomsOrderRequest.setShipperCountry(sender.getCountry());
        tomsOrderRequest.setShipperPostcode(sender.getPostCode());
        if (orderInfo.getReceiver().getStreet().length() <= 200) {
            tomsOrderRequest.setShipperAddressLine1(StringUtils.substring(sender.getStreet(), 0, 200));
        } else if (sender.getStreet().length() > 200 && sender.getStreet().length() <= 400) {
            tomsOrderRequest.setShipperAddressLine1(StringUtils.substring(sender.getStreet(), 0, 200));
            tomsOrderRequest.setShipperAddressLine2(StringUtils.substring(sender.getStreet(), 200, 400));
        } else if (sender.getStreet().length() > 400 && sender.getStreet().length() <= 480) {
            tomsOrderRequest.setShipperAddressLine1(StringUtils.substring(sender.getStreet(), 0, 200));
            tomsOrderRequest.setShipperAddressLine2(StringUtils.substring(sender.getStreet(), 200, 400));
            tomsOrderRequest.setShipperAddressLine3(StringUtils.substring(sender.getStreet(), 400, 80));
        } else if (sender.getStreet().length() > 480) {
            throw new BusinessServiceException("发件人地址不能大于480个字符");
        }
    }

    private String calculate(String secretAccessKey, String data) {
        try {
            SecretKeySpec signingKey = new SecretKeySpec(secretAccessKey.getBytes("UTF-8"), HMAC_SHA1_ALGORITHM);

            Mac mac = Mac.getInstance(HMAC_SHA1_ALGORITHM);
            mac.init(signingKey);

            byte[] rawHmac = mac.doFinal(data.getBytes("UTF-8"));
            return new String(Base64.encode(rawHmac), "UTF-8");
        } catch (Exception e) {
            throw new IllegalStateException(e);
        }
    }


    public MultiValueMap<String, String> buildHeader(String method, String url, String token, String secret) {
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<String, String>();
        SimpleDateFormat format = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss zzz", Locale.US);
        format.setTimeZone(TimeZone.getTimeZone("GMT"));
        String dateStr = format.format(new Date());
        StringBuilder sb = new StringBuilder();
        sb.append(method).append(NEW_LINE).append(dateStr).append(NEW_LINE).append(url);
        String authorization = MessageFormat.format(HEADERS_AUTHORIZATION_PREFIX + "{0}:{1}", token, calculate(secret, sb.toString()));

        headers.add("X-WallTech-Date", dateStr);
        headers.add("Authorization", authorization);
        headers.add("Content-Type", "application/json");
        return headers;
    }

    private void buildItemInfo(ETowerCreateOrderRequest tomsOrderRequest, List<OrderItemInfo> orderItemInfoList) {
        List<ETowerCreateOrderItemInfo> orderItemList = new ArrayList<>();
        orderItemInfoList.forEach(detail -> {
            ETowerCreateOrderItemInfo orderItemInfo = new ETowerCreateOrderItemInfo();
            orderItemInfo.setItemNo(detail.getOid());
            orderItemInfo.setSku(detail.getSku());
            orderItemInfo.setDescription(detail.getEnName());
            orderItemInfo.setItemCount(detail.getCount());
            orderItemInfo.setOriginCountry("CN");
            orderItemInfo.setNativeDescription(detail.getCnName());
            orderItemInfo.setUnitValue(detail.getUnitPrice());
            orderItemInfo.setWeight(detail.getWeight());
            orderItemList.add(orderItemInfo);
        });
        tomsOrderRequest.setOrderItems(orderItemList);
    }

    private <T> T postToETower(String requestContent, Map<String, String> configMap, String url, Class<T> responseClass) {
        HttpHeaders headers = new HttpHeaders();
        String requestUrl = String.format("%s%s", eTowerUrl, url);
        headers.addAll(this.buildHeader("POST", requestUrl, configMap.get(APP_TOKEN), configMap.get(APP_SECRET)));
        HttpEntity<String> entity = new HttpEntity<>(requestContent, headers);
        LOGGER.info("e-tower请求接口URL为：{},请求参数为：{}", requestUrl, requestContent);
        return this.restTemplate.exchange(requestUrl, HttpMethod.POST, entity, responseClass).getBody();
    }

    private String downloadLabelPdfAndUploadOSS(String base64Str, String trackingNumber) {
        try {
            byte[] pdfByte = Base64.decode(base64Str);
            String labelFileName = String.format(LABEL_NAME, labelFolder, trackingNumber);
            return getPdfLabel(pdfByte, labelFileName);
        } catch (Exception e) {
            LOGGER.error("e-tower面单无法获取: " + e.getMessage(), e);
            return "";
        }
    }
}
