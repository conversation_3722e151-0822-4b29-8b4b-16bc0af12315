package com.nsy.api.tms.service.external;

import com.nsy.api.core.apicore.util.DateUtils;
import com.nsy.api.core.apicore.util.JSONUtils;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.MyObjectMapper;
import com.nsy.api.tms.dao.entity.TmsLogisticsChannelConfigEntity;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.dao.entity.TmsRouteRecordEntity;
import com.nsy.api.tms.enumeration.PackageStatusEnum;
import com.nsy.api.tms.logistics.ups.track.Activity;
import com.nsy.api.tms.logistics.ups.track.ActivityAddress;
import com.nsy.api.tms.logistics.ups.track.ServiceAccessToken;
import com.nsy.api.tms.logistics.ups.track.TrackRequest;
import com.nsy.api.tms.logistics.ups.track.TrackRequestDetail;
import com.nsy.api.tms.logistics.ups.track.TrackResponse;
import com.nsy.api.tms.logistics.ups.track.UpsSecurity;
import com.nsy.api.tms.logistics.ups.track.UpsTrackRequest;
import com.nsy.api.tms.logistics.ups.track.UpsTransactionReference;
import com.nsy.api.tms.logistics.ups.track.UpsUsernameToken;
import com.nsy.api.tms.service.PackageService;
import com.nsy.api.tms.service.TmsLogisticsChannelConfigService;
import com.nsy.api.tms.service.TmsRequestLogService;
import com.nsy.api.tms.service.TmsRouteService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;
import java.io.IOException;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: Woods Lee
 * @Date: 2019/1/17 14:52
 */
@Service
public class UpsTrackService extends UpsService {
    private static final Logger LOGGER = LoggerFactory.getLogger(UpsTrackService.class);
    @Value("${ups.track.url}")
    private String endpointUrl;

    @Inject
    TmsRequestLogService tmsRequestLogService;

    @Inject
    PackageService packageService;

    @Inject
    TmsRouteService tmsRouteService;

    @Inject
    TmsLogisticsChannelConfigService logisticsChannelConfigService;

    @Inject
    RestTemplate restTemplate;

    @Inject
    MyObjectMapper myObjectMapper;


    private Map<String, String> buildTrackConfig(TmsLogisticsChannelConfigEntity channelConfigEntity) {
        String keyGroup = channelConfigEntity.getKeyGroup();
        if ("stage".equalsIgnoreCase(env.getActiveProfiles()[0])) {
            keyGroup = keyGroup + "_Track";
        }
        return tmsConfigService.getConfigMap(keyGroup);

    }

    private UpsTrackRequest populateTrackRequest(TmsPackageEntity tmsPackageEntity, Map<String, String> configMap) {
        UpsTrackRequest upsTrackRequest = new UpsTrackRequest();
        UpsSecurity security = new UpsSecurity();
        ServiceAccessToken accessToken = new ServiceAccessToken();
        accessToken.setAccessLicenseNumber(configMap.get(CONFIG_TOKEN));
        security.setServiceAccessToken(accessToken);
        UpsUsernameToken usernameToken = new UpsUsernameToken();
        usernameToken.setUsername(configMap.get(CONFIG_USER_NAME));
        usernameToken.setPassword(configMap.get(CONFIG_PASSWORD));
        security.setUsernameToken(usernameToken);
        TrackRequest trackRequest = new TrackRequest();
        upsTrackRequest.setTrackRequest(trackRequest);
        upsTrackRequest.setUpsSecurity(security);
        TrackRequestDetail trackRequestDetail = new TrackRequestDetail();
        trackRequest.setTrackRequestDetail(trackRequestDetail);
        trackRequest.setInquiryNumber(tmsPackageEntity.getLogisticsNo());
        UpsTransactionReference upsTransactionReference = new UpsTransactionReference();
        trackRequestDetail.setTransactionReference(upsTransactionReference);
        trackRequestDetail.setRequestOption("01");
        upsTransactionReference.setCustomerContext("shiying api");
        return upsTrackRequest;
    }

    /**
     * I = In Transit
     * D = Delivered
     * X = Exception
     * P = Pickup
     * M = Manifest Pickup.
     *
     * @param packageEntity
     */
    public void doTrack(TmsPackageEntity packageEntity) {
        // 记录日志
        TmsRequestLogEntity logEntity = tmsRequestLogService.recordBaseInfoLog(packageEntity);
        //更新路由查询次数
        packageEntity.setRouteSearchTimes(packageEntity.getRouteSearchTimes() + 1);
        packageService.save(packageEntity);
        TmsLogisticsChannelConfigEntity configEntity = logisticsChannelConfigService.findByLogisticsMethodAndKeyGroupAndLogisticsChannelCodeAndStatus(packageEntity.getLogisticsMethod(), packageEntity.getKeyGroup(), packageEntity.getLogisticsChannelCode(), TmsLogisticsChannelConfigEntity.ACTIVE);
        Map<String, String> configMap = buildTrackConfig(configEntity);
        UpsTrackRequest upsTrackRequest = null;
        try {
            upsTrackRequest = populateTrackRequest(packageEntity, configMap);
            LOGGER.info("LogisticsNo:{}, request:{}", packageEntity.getLogisticsNo(), JSONUtils.toJSON(upsTrackRequest));
            String trackResponse = restTemplate.postForObject(endpointUrl, upsTrackRequest, String.class);
            LOGGER.info("LogisticsNo:{}, response:{}", packageEntity.getLogisticsNo(), trackResponse);
            if (trackResponse.contains("Success")) {
                //如果调用成功
                parseSuccessResponse(packageEntity, trackResponse);
                tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, JSONUtils.toJSON(upsTrackRequest), trackResponse, packageEntity.getLogisticsNo());
            } else {
                // parseFailedResponse(upsTrackRequest, trackResponse, packageEntity.getLogisticsNo());
                tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, JSONUtils.toJSON(upsTrackRequest), trackResponse, packageEntity.getLogisticsNo());
                LOGGER.info("LogisticsNo:{},路由查询异常", packageEntity.getLogisticsNo(), JSONUtils.toJSON(upsTrackRequest));
            }
        } catch (Exception e) {
            tmsRequestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, JSONUtils.toJSON(upsTrackRequest), e.getMessage(), packageEntity.getLogisticsNo());
            throw new RuntimeException(e);
        }
    }

//    private void parseFailedResponse(UpsTrackRequest trackRequest, String response, String logisticsNo) {
//        TmsRequestLogEntity tmsRequestLogEntity = new TmsRequestLogEntity
//                .Builder(LogisticsMethodEnum.UPS.getLogisticsMethod()
//                , "track-api", TmsRequestLogEntity.FAIL, JSONUtils.toJSON(trackRequest)
//                , response).logisticsNo(logisticsNo).build();
//        tmsRequestLogService.save(tmsRequestLogEntity);
//    }

    private void parseSuccessResponse(TmsPackageEntity packageEntity, String response) throws ParseException, IOException {
        //解析success结果
        TrackResponse trackResponse = myObjectMapper.readValue(response, TrackResponse.class);
        // 3. 更新tms_package状态，同时，写路由表（当前查询路由信息与上一条路由表记录状态一致，则不重新插入）
        Activity latestTrackActivity = trackResponse.getUpsTrackResponse().getShipmentReply().getUpsPackages().get(0).getActivity().get(0);
        if (latestTrackActivity == null && StringUtils.hasText(latestTrackActivity.getStatus().getType())) {
            LOGGER.info("logisticsNo:{}, 暂无路由信息", packageEntity.getLogisticsNo());
            return;
        }
        //最近一次物流时间
        String latestTimeStr = latestTrackActivity.getDate() + latestTrackActivity.getTime();
        Date latestTrackTime = DateUtils.parse(latestTimeStr, "yyyyMMddHHmmss");
        // 地址：city,stateOrProvinceCode,countryCode
        ActivityAddress address = latestTrackActivity.getActivityLocationType().getAddress();
        String latestTrackLocation = String.format("%s,%s,%s", address.getCity(), address.getStateProvinceCode(), address.getCountryCode());
        List<TmsRouteRecordEntity> routeRecordEntityList = tmsRouteService.findByLogisticsNoOrderByCreateDateDesc(packageEntity.getLogisticsNo());
        // 更新或保存路由信息
        if (routeRecordEntityList.isEmpty() || !(latestTrackActivity.getStatus().getType().equalsIgnoreCase(routeRecordEntityList.get(0).getStatus()) && latestTrackTime.
                compareTo(routeRecordEntityList.get(0).getAcceptTime()) == 0 && latestTrackLocation.equalsIgnoreCase(routeRecordEntityList.get(0).getAcceptAddress()))) {
            //保存最新一条路由记录
            saveTmsRouteRecord(packageEntity.getLogisticsNo(), latestTrackActivity, latestTrackTime, latestTrackLocation);
        }

        if ("M".equalsIgnoreCase(latestTrackActivity.getStatus().getType())) {
            packageEntity.setStatus(PackageStatusEnum.SHIPPED.getDesc());
        } else if ("D".equalsIgnoreCase(latestTrackActivity.getStatus().getType())) {
            packageEntity.setStatus(PackageStatusEnum.DELIVERED.getDesc());
            packageEntity.setArrivalTime(latestTrackTime);
        } else if ("P".equalsIgnoreCase(latestTrackActivity.getStatus().getType())) {
            packageEntity.setStatus(PackageStatusEnum.TAKEN.getDesc());
        } else {
            packageEntity.setStatus(PackageStatusEnum.TRANSIT.getDesc());
        }
        if (Objects.isNull(packageEntity.getReceiveTime())) {
            for (Activity activity : trackResponse.getUpsTrackResponse().getShipmentReply().getUpsPackages().get(0).getActivity()) {
                if ("P".equalsIgnoreCase(activity.getStatus().getType())) {
                    packageEntity.setReceiveTime(DateUtils.parse(activity.getDate() + activity.getTime(), "yyyyMMddHHmmss"));
                }
            }
            if (Objects.isNull(packageEntity.getReceiveTime())) {
                packageEntity.setReceiveTime(DateUtils.parse(trackResponse.getUpsTrackResponse().getShipmentReply().getPickupDate(), "yyyyMMdd"));
            }
        }
        packageEntity.setRouteLastUpdateTime(latestTrackTime);
        packageService.save(packageEntity);

    }

    private void saveTmsRouteRecord(String logisticsNo, Activity latestTrackActivity, Date latestTrackTime, String latestTrackLocation) {
        TmsRouteRecordEntity routeRecordEntity = new TmsRouteRecordEntity();
        routeRecordEntity.setStatus(latestTrackActivity.getStatus().getType());
        routeRecordEntity.setAcceptAddress(latestTrackLocation);
        routeRecordEntity.setAcceptTime(latestTrackTime);
        routeRecordEntity.setLogisticsNo(logisticsNo);
        routeRecordEntity.setRemark(latestTrackActivity.getStatus().getDescription());
        tmsRouteService.save(routeRecordEntity);
    }


}
