package com.nsy.api.tms.service.external.refactor;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.UnicodeUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.annotation.LogisticsServiceHandler;
import com.nsy.api.tms.dao.entity.TmsLogisticsAccountEntity;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.domain.Address;
import com.nsy.api.tms.domain.OrderItemInfo;
import com.nsy.api.tms.domain.PrintListResponse;
import com.nsy.api.tms.domain.order.OrderNewInfo;
import com.nsy.api.tms.enumeration.LogisticsMethodEnum;
import com.nsy.api.tms.logistics.shanda.GoodsRequest;
import com.nsy.api.tms.logistics.shanda.ShanDaDetailResponse;
import com.nsy.api.tms.logistics.shanda.ShanDaGood;
import com.nsy.api.tms.logistics.shanda.ShanDaOrderResponse;
import com.nsy.api.tms.request.OrderNewRequest;
import com.nsy.api.tms.request.StringListRequest;
import com.nsy.api.tms.response.GenerateOrderResponse;
import com.nsy.api.tms.response.SecondaryNumberResponse;
import com.nsy.api.tms.utils.JsonMapper;
import com.nsy.api.tms.utils.Validator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.inject.Inject;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * HXD
 * 2021/5/11
 **/
@Service
@LogisticsServiceHandler(logisticsMethod = LogisticsMethodEnum.SHANDA)
public class ShanDaService extends BaseLogisticsNewService implements InitializingBean {

    private static final Logger LOGGER = LoggerFactory.getLogger(ShanDaService.class);

//    private static final String LABEL_NAME = "%ShanDa-%s";

    public static final String LINE_ID = "line-id";
    public static final String EXPRESS_ID = "express-id";
    public static final String SPACE_ID = "space-id";
    public static final String API_KEY = "Api-Key";
    public static final String CREATE_ORDER = "/saveTransportOrderForApi";
    public static final String ORDER_DETAIL = "/getTransportOrderDetail?platform_order_sn=";
//    public static final String DOWN_LOAD_LABEL = "/printOrderLabel?platform_order_sn={}&file_type=pdf";
//    public static final String GET_LABEL_METHOD = "express.order.label.get";


    @Value("${shanda.server.url}")
    private String shanDaBaseUrl;
    @Inject
    private RestTemplate restTemplate;


    @Override
    public void preDeal(OrderNewRequest request) {
        reSetUserPriceByTotalNum(request.getOrderInfo().getOrderItemInfoList(), request);
    }

    @Override
    protected GoodsRequest buildLogisticsOrderRequest(OrderNewRequest orderRequest, Map<String, String> configMap) {
        GoodsRequest request = new GoodsRequest();
        OrderNewInfo orderInfo = orderRequest.getOrderInfo();
        request.setGoodsType("1");
        // 平台订单号 （需具备唯一性
        request.setPlatformOrderSn(orderInfo.getTmsTid());
        // 其他ERP请咨询业务人员  平台类型
        request.setPlatformType("4");
        // 渠道id
        request.setExpressId(configMap.get(EXPRESS_ID));
        // 线路ID 见渠道接口返回值二级线路内的id
        request.setLineId(configMap.get(LINE_ID));
        request.setWid(configMap.get(SPACE_ID));
        //预计到仓库时间 2023-10-20
        request.setEsTime(DateUtil.format(DateUtil.offsetDay(new Date(), 3), DatePattern.NORM_DATE_FORMAT));
        // 1 快递送仓 2上门取货 3自行送仓
        request.setTransportType("1");
        List<ShanDaGood> goodList = new ArrayList<>();
        for (OrderItemInfo itemInfo : orderInfo.getOrderItemInfoList()) {
            ShanDaGood product = new ShanDaGood();
            product.setPackageCode(IdUtil.simpleUUID());
            product.setGoodsCount(String.valueOf(itemInfo.getCount()));
            product.setWeight(String.valueOf(itemInfo.getWeight()));
            product.setGoodsPrice(String.valueOf(itemInfo.getCustomsPrice()));
            product.setEnGoodsName(itemInfo.getEnName());
            product.setCnGoodsName(itemInfo.getCnName());
            product.setHsCode(itemInfo.getHsCode());
            goodList.add(product);
        }
        request.setGoodsList(goodList);
        buildReceiver(request, orderInfo);
        return request;
    }

    private void buildReceiver(GoodsRequest request, OrderNewInfo orderInfo) {
        Address receiver = orderInfo.getReceiver();
        request.setRecipientName(receiver.getName());
        request.setRecipientPhone(StringUtils.hasText(receiver.getPhone()) ? receiver.getPhone() : receiver.getMobile());
        request.setRecipientEmail(receiver.getEmail());
        request.setRecipientCountry(orderInfo.getReceiveCountryCode());
        request.setRecipientState(receiver.getProvince());
        request.setRecipientCity(receiver.getCity());
        request.setRecipientPostCode(receiver.getPostCode());
        request.setRecipientAddress(receiver.getStreet());
    }


    @Override
    public SecondaryNumberResponse getSecondaryNumber(TmsPackageEntity packageEntity) {
        return super.getSecondaryNumber(packageEntity);
    }

    @Override
    public GenerateOrderResponse doRequest(OrderNewRequest orderRequest, Map<String, String> configMap, String requestContent) {
        TmsRequestLogEntity logEntity = requestLogService.recordBaseInfoLog(orderRequest.getOrderInfo());
        GenerateOrderResponse response = new GenerateOrderResponse();
        try {
            HttpHeaders headers = new HttpHeaders();
            String orderUrl = shanDaBaseUrl + CREATE_ORDER;
            headers.add("Content-Type", MediaType.APPLICATION_JSON_UTF8_VALUE);
            headers.add("Api-Key", configMap.get(API_KEY));
            HttpEntity<String> entity = new HttpEntity<>(requestContent, headers);
            ResponseEntity<ShanDaOrderResponse> responseEntity = this.restTemplate.exchange(orderUrl, HttpMethod.POST, entity, ShanDaOrderResponse.class);
            if (responseEntity.getBody() != null && Objects.equals(responseEntity.getBody().getCode(), 200)) {
                LOGGER.info("{}闪达下单：成功返回：{}", orderRequest.getOrderInfo().getBusinessKey(), JsonMapper.toJson(responseEntity.getBody()));
                ShanDaDetailResponse detailResp = JsonMapper.fromJson(JsonMapper.toJson(responseEntity.getBody().getData()), ShanDaDetailResponse.class);
                // 获取转运单号和面单
                HttpHeaders matchHeader = new HttpHeaders();
                matchHeader.add("Api-Key", configMap.get(API_KEY));
                HttpEntity<String> matchEntity = new HttpEntity<>(requestContent, matchHeader);
                ResponseEntity<ShanDaOrderResponse> matchResponse = this.restTemplate.exchange(shanDaBaseUrl + ORDER_DETAIL + detailResp.getTransportOrderSn(), HttpMethod.GET, matchEntity, ShanDaOrderResponse.class);
                if (matchResponse.getBody() != null && Objects.equals(matchResponse.getBody().getCode(), 200)) {
                    ShanDaDetailResponse data = JsonMapper.fromJson(JsonMapper.toJson(matchResponse.getBody().getData()), ShanDaDetailResponse.class);
                    //                    ResponseEntity<ShanDaOrderResponse> labelResp = this.restTemplate.exchange(StrUtil.format(shanDaBaseUrl + DOWN_LOAD_LABEL, detailResp.getTransportOrderSn()), HttpMethod.GET, matchEntity, ShanDaOrderResponse.class);
                    //                    ShanDaLabelResponse labelData = JsonMapper.fromJson(JsonMapper.toJson(labelResp.getBody().getData()), ShanDaLabelResponse.class);
                    //                    byte[] bytes = FileUtils.downloadPdf(labelData.getLabelUrl(), restTemplate);
                    //                    RequestEntity requestEntity = RequestEntity.get(new URI(detailResp.getLabelUrl())).build();
                    //                    ResponseEntity<String> responseEntity1 = restTemplate.exchange(requestEntity, String.class);
                    //                    byte[] bytes = HtmlConvertPdfUtils.htmlToPdfByte(responseEntity1.getBody().replaceAll("<meta charset=\"UTF-8\">", "")
                    //                            .replaceAll("<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">", "")
                    //                            .replaceAll("<br>", "</img><br></br>").replaceAll("&nbsp;", " "));
                    //                    String pdfUrl = getPdfLabel(bytes, String.format(LABEL_NAME, labelFolder, data.getExpressSn()));
                    GenerateOrderResponse.SuccessEntity successEntity = buildSuccessEntity(orderRequest.getOrderInfo(), data.getExpressSn(), detailResp.getLabelUrl(), detailResp.getTransportOrderSn(), null);
                    response.setSuccessEntity(successEntity);
                    requestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, requestContent, JsonMapper.toJson(responseEntity), successEntity.getLogisticsNo());
                } else {
                    requestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, requestContent, UnicodeUtil.toString(JsonMapper.toJson(matchResponse)), null);
                    response.setError(buildError("400", "获取单号详情错误：" + judgeResponse(matchResponse)));
                }
            } else {
                requestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, requestContent, JsonMapper.toJson(responseEntity), null);
                response.setError(buildError("400", "下单失败：" + judgeResponse(responseEntity)));
            }
        } catch (Exception e) {
            requestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, requestContent, e.getMessage(), null);
            LOGGER.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        return response;
    }

    private String judgeResponse(ResponseEntity<ShanDaOrderResponse> resp) {
        if (resp.getBody() == null) {
            return JsonMapper.toJson(resp);
        }
        ShanDaOrderResponse body = resp.getBody();
        if (StringUtils.hasText(body.getMessage())) {
            return UnicodeUtil.toString(body.getMessage());
        }
        return JsonMapper.toJson(resp);
    }


    @Override
    public void validateOrderRequest(OrderNewRequest request) {
        OrderNewInfo orderInfo = request.getOrderInfo();
        super.validateOrderRequest(request);
        Validator.isValid(orderInfo.getReceiver(), attr -> !Objects.isNull(attr), "收件人不能为空");
        Validator.isValid(orderInfo.getReceiver().getPostCode(), StringUtils::hasText, "收件人邮编不能为空");
        Validator.isValid(orderInfo.getReceiver().getStreet(), attr -> attr.length() < 200, "地址长度超过限制");
        if (!StrUtil.equalsIgnoreCase(orderInfo.getReceiveCountryCode(), "US")) {
            throw new BusinessServiceException("闪达无法走美国以外的包裹！");
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.labelFolder += "ShanDa/";
        this.ossLabelFolder += "ShanDa/";
    }

    @Override
    protected String buildSerializedRequest(OrderNewRequest request, TmsLogisticsAccountEntity logisticsAccountEntity) {
        Map<String, String> configMap = buildAccountConfig(logisticsAccountEntity, request.getOrderInfo());
        GoodsRequest request1 = buildLogisticsOrderRequest(request, configMap);
        return JsonMapper.toJson(request1);
    }


    public PrintListResponse getLabelHtml(StringListRequest stringListRequest) {
        PrintListResponse response = new PrintListResponse();
        response.setSpec("100*160");
        response.setHtmlList(new ArrayList<>());
        stringListRequest.getStringList().forEach(item -> {
            try {
                RequestEntity requestEntity = RequestEntity.get(new URI(item)).build();
                ResponseEntity<String> responseEntity = restTemplate.exchange(requestEntity, String.class);
                response.getHtmlList().add(responseEntity.getBody());
            } catch (URISyntaxException e) {
                e.printStackTrace();
            }
        });
        return response;
    }


}
