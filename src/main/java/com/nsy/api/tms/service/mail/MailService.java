package com.nsy.api.tms.service.mail;

import com.nsy.api.core.apicore.util.BeanUtilsEx;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.mail.MailProperties;
import org.springframework.core.env.Environment;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import java.util.Arrays;
import java.util.Date;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * created by jun.
 **/
@Service
public class MailService {
    private static final Logger LOGGER = LoggerFactory.getLogger(MailService.class);

    @Autowired
    private JavaMailSender mailSender;

    @Autowired
    private MailProperties mailProperties;

    @Autowired
    private Environment env;

    private final ExecutorService executorService = Executors.newFixedThreadPool(5);

    public void send(SendSimpleMailRequest sendMailRequest) {
        try {
            SimpleMailMessage simpleMailMessage = new SimpleMailMessage();
            BeanUtilsEx.copyProperties(sendMailRequest, simpleMailMessage);
            simpleMailMessage.setSubject(String.format("%s%s", Arrays.asList(env.getActiveProfiles()), sendMailRequest.getSubject()));
            simpleMailMessage.setSentDate(new Date());
            simpleMailMessage.setFrom(mailProperties.getUsername());
            mailSender.send(simpleMailMessage);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
        }

    }

    public void asyncSend(SendSimpleMailRequest sendMailRequest) {
        executorService.submit(() -> this.send(sendMailRequest));
    }

    @PreDestroy
    public void destroy() {
        Optional.ofNullable(executorService).ifPresent(ExecutorService::shutdownNow);
    }
}
