package com.nsy.api.tms.service.external.refactor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.nsy.api.core.apicore.util.StringUtils;
import com.nsy.api.tms.dao.entity.TmsAlertTaskEntity;
import com.nsy.api.tms.dao.entity.TmsLogisticsAccountEntity;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.domain.OrderItemInfo;
import com.nsy.api.tms.domain.order.OrderNewInfo;
import com.nsy.api.tms.enumeration.LogisticsMethodEnum;
import com.nsy.api.tms.logistics.hualei.HuaLeiLogisticsOrderRequest;
import com.nsy.api.tms.logistics.hualei.HuaLeiLogisticsOrderResponse;
import com.nsy.api.tms.repository.TmsAlertTaskRepository;
import com.nsy.api.tms.request.OrderNewRequest;
import com.nsy.api.tms.response.GenerateOrderResponse;
import com.nsy.api.tms.response.PrintLabelResponse;
import com.nsy.api.tms.response.SecondaryNumberResponse;
import com.nsy.api.tms.utils.JsonMapper;
import com.nsy.api.tms.utils.Validator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class HuaLeiNewService extends BaseLogisticsNewService {
    protected static final Logger LOGGER = LoggerFactory.getLogger(HuaLeiNewService.class);

    @Autowired
    HuaLeiTrackNewService huaLeiTrackService;
    @Autowired
    TmsAlertTaskRepository alertTaskRepository;

    public static final String CONFIG_USER_NAME = "userName";
    public static final String CONFIG_PASSWORD = "password";
    public static final String CONFIG_CUSTOMER_ID = "customer_id";
    public static final String CONFIG_USER_ID = "customer_userid";

    protected String createOrderUrl = "";
    protected String printLabelUrl = "";
    protected String hostIP = "";
    protected String authenticationUrl = "";
    protected String labelNameTemplate = "";
    protected String trackUrl = "";
    protected String trackingNumberUrl = "";

    @Override
    public GenerateOrderResponse syncGenerateOrder(OrderNewRequest request, TmsLogisticsAccountEntity logisticsAccountEntity) {
        TmsRequestLogEntity logEntity = requestLogService.recordBaseInfoLog(request.getOrderInfo());
        GenerateOrderResponse response = new GenerateOrderResponse();
        Map<String, String> configMap = buildAccountConfig(logisticsAccountEntity, request.getOrderInfo());
        HuaLeiLogisticsOrderRequest huaLeiLogisticsOrderRequest = null;
        try {
            huaLeiLogisticsOrderRequest = buildLogisticsOrderRequest(request, configMap);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("param", JsonMapper.toJson(huaLeiLogisticsOrderRequest));
            LOGGER.info("物流公司：{}，参数：{}", this.getClass().getSimpleName(), JsonMapper.toJson(huaLeiLogisticsOrderRequest));
            HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<>(params, headers);
            String reply = restTemplate.postForObject(createOrderUrl, httpEntity, String.class);
            HuaLeiLogisticsOrderResponse replyObject = JsonMapper.fromJson(reply, HuaLeiLogisticsOrderResponse.class);
            LOGGER.info(JsonMapper.toJson(replyObject));
            if (isResponseOk(replyObject)) {
                GenerateOrderResponse.SuccessEntity successEntity = processSuccessReply(request.getOrderInfo(), replyObject);
                response.setSuccessEntity(successEntity);
                requestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, JsonMapper.toJson(huaLeiLogisticsOrderRequest), reply, successEntity.getLogisticsNo());
            } else {
                GenerateOrderResponse.Error error = processFailReply(replyObject);
                response.setError(error);
                requestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, JsonMapper.toJson(huaLeiLogisticsOrderRequest), reply, null);
            }
        } catch (Exception e) {
            requestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, JsonMapper.toJson(huaLeiLogisticsOrderRequest), e.getMessage(), null);
            throw new RuntimeException(e);
        }
        return response;
    }

    @Override
    public Map<String, String> buildAccountConfig(TmsLogisticsAccountEntity logisticsAccountEntity, OrderNewInfo orderInfo) {
        Map<String, String> configMap = super.buildAccountConfig(logisticsAccountEntity, orderInfo);
        String api = this.authenticationUrl + "?username={username}&password={password}";
        Map<String, String> requestParam = new HashMap<>();
        requestParam.put("username", configMap.get(CONFIG_USER_NAME));
        requestParam.put("password", configMap.get(CONFIG_PASSWORD));
        Map<String, String> map = restTemplate.getForObject(api, Map.class, requestParam);
        if (map != null && StringUtils.hasText(map.get(CONFIG_CUSTOMER_ID))) {
            configMap.put(CONFIG_CUSTOMER_ID, map.get(CONFIG_CUSTOMER_ID));
        }
        if (map != null && StringUtils.hasText(map.get(CONFIG_CUSTOMER_ID))) {
            configMap.put(CONFIG_USER_ID, map.get(CONFIG_USER_ID));
        }
        return configMap;
    }

    @Override
    protected HuaLeiLogisticsOrderRequest buildLogisticsOrderRequest(OrderNewRequest request, Map<String, String> configMap) {
        OrderNewInfo orderInfo = request.getOrderInfo();
        HuaLeiLogisticsOrderRequest huaLeiOrderRequest = new HuaLeiLogisticsOrderRequest();
        huaLeiOrderRequest.setCustomerId(configMap.get(CONFIG_CUSTOMER_ID));
        huaLeiOrderRequest.setCustomerUserId(configMap.get(CONFIG_USER_ID));
        huaLeiOrderRequest.setOrderInvoiceParam(new ArrayList<>());
        List<OrderItemInfo> orderItemInfoList = orderInfo.getOrderItemInfoList();

        huaLeiOrderRequest.setOrderInvoiceParam(buildOrderInvoiceParam(orderInfo.getOrderItemInfoList(), orderInfo.getLogisticsChannelCode()));
        Double orderAmount = orderItemInfoList.stream().filter(x -> x.getUnitPrice() != null).mapToDouble(orderItem -> orderItem.getUnitPrice() * orderItem.getCount()).sum();
        huaLeiOrderRequest.setOrderCargoAmount(String.valueOf(orderAmount));
        huaLeiOrderRequest.setTradeType("ZYXT");
        huaLeiOrderRequest.setProductId(orderInfo.getLogisticsChannelCode());
        huaLeiOrderRequest.setOrderCustomerInvoicecode(orderInfo.getTmsTid());
        if (orderInfo.getSender() != null) {
            huaLeiOrderRequest.setShipperCountry(orderInfo.getSender().getCountry());
            huaLeiOrderRequest.setShipperAddress1(orderInfo.getSender().getStreet());
            huaLeiOrderRequest.setShipperPostCode(orderInfo.getSender().getPostCode());
            huaLeiOrderRequest.setShipperCompanyName(orderInfo.getSender().getCompany());
            huaLeiOrderRequest.setShipperCity(orderInfo.getSender().getCity());
            huaLeiOrderRequest.setShipperState(orderInfo.getSender().getProvince());
            huaLeiOrderRequest.setShipperTelephone(StringUtils.hasText(orderInfo.getSender().getPhone()) ? orderInfo.getSender().getPhone() : orderInfo.getSender().getMobile());
            huaLeiOrderRequest.setShipperName(orderInfo.getSender().getName());
        }
        huaLeiOrderRequest.setConsigneeAddress(orderInfo.getReceiver().getStreet());
        huaLeiOrderRequest.setConsigneePostCode(orderInfo.getReceiver().getPostCode());
        huaLeiOrderRequest.setConsigneeCompanyName(StringUtils.hasText(orderInfo.getReceiver().getCompany()) ? orderInfo.getReceiver().getCompany() : orderInfo.getReceiver().getName());
        huaLeiOrderRequest.setConsigneeCity(orderInfo.getReceiver().getCity());
        huaLeiOrderRequest.setConsigneeState(orderInfo.getReceiver().getProvince());
        huaLeiOrderRequest.setCountry(orderInfo.getReceiver().getCountry());
        huaLeiOrderRequest.setConsigneeTelephone(StringUtils.hasText(orderInfo.getReceiver().getPhone()) ? orderInfo.getReceiver().getPhone() : orderInfo.getReceiver().getMobile());
        huaLeiOrderRequest.setConsigneeMobile(StringUtils.hasText(orderInfo.getReceiver().getMobile()) ? orderInfo.getReceiver().getMobile() : orderInfo.getReceiver().getPhone());
        huaLeiOrderRequest.setConsigneeName(orderInfo.getReceiver().getName());
        buildIossNumber(orderInfo, huaLeiOrderRequest);
        return huaLeiOrderRequest;
    }

    private List<HuaLeiLogisticsOrderRequest.OrderInvoiceParam> buildOrderInvoiceParam(List<OrderItemInfo> orderItemInfoList, String logisticsChannelCode) {
        List<HuaLeiLogisticsOrderRequest.OrderInvoiceParam> orderInvoiceParamList = new ArrayList<>();
        orderItemInfoList.forEach((orderItem) -> {
            HuaLeiLogisticsOrderRequest.OrderInvoiceParam orderInvoiceParam = new HuaLeiLogisticsOrderRequest.OrderInvoiceParam();
            orderInvoiceParam.setHsCode(orderItem.getHsCode());
            orderInvoiceParam.setInvoiceTitle(orderItem.getEnName());
            orderInvoiceParam.setSku(getSkuByByService(orderItem, logisticsChannelCode));
            orderInvoiceParam.setInvoicePcs(orderItem.getCount());
            orderInvoiceParam.setInvoiceWeight(orderItem.getWeight() / orderItem.getCount());
            orderInvoiceParam.setInvoiceAmount(orderItem.getCustomsPrice() * orderItem.getCount());
            buildItemInvoice(orderItem, orderInvoiceParam, logisticsChannelCode);
            orderInvoiceParamList.add(orderInvoiceParam);
        });
        return orderInvoiceParamList;
    }

    protected void buildItemInvoice(OrderItemInfo orderItem, HuaLeiLogisticsOrderRequest.OrderInvoiceParam orderInvoiceParam, String logisticsChannelCode) { }

    protected void buildIossNumber(OrderNewInfo orderInfo, HuaLeiLogisticsOrderRequest huaLeiOrderRequest) { }

    /**
     * 广州分公司使用的物流，面单需要用SKU
     */
    protected String getSkuByByService(OrderItemInfo orderItem, String channelCode) {
        return orderItem.getEnName();
    }

    private GenerateOrderResponse.SuccessEntity processSuccessReply(OrderNewInfo orderInfo, HuaLeiLogisticsOrderResponse response) {
        String orderId = response.getOrderId();
        String trackingNumber = getTrackingNumber(orderInfo, response);
        String labelUrl = getLabel(response.getOrderId());
        return buildSuccessEntity(orderInfo, trackingNumber, labelUrl, orderId, null);
    }

    private String getTrackingNumber(OrderNewInfo orderInfo, HuaLeiLogisticsOrderResponse response) {
        // 大陆DHL(小包)运单号为请求的tmsTid
        if (LogisticsMethodEnum.JYEXPRESS.getLogisticsMethod().equals(orderInfo.getLogisticsMethod())
                && ("1842".equals(orderInfo.getLogisticsChannelCode()) || "9862".equalsIgnoreCase(orderInfo.getLogisticsChannelCode()))) {
            return response.getReferenceNumber();
        } else {
            return response.getTrackingNumber();
        }
    }

    protected boolean isPdfLabel() {
        return true;
    }


    private String getLabel(String orderId) {
        String labelUrl;
        try {
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("PrintType", "lab10_10");
            params.add("order_id", orderId);
            URI uri = restTemplate.postForLocation(printLabelUrl, params);
            if (null == uri) {
                LOGGER.error("获取面单失败，printLabelUrl:{},", String.format("%s?PrintType=lab10_10&order_id=%s", printLabelUrl, orderId));
                return null;
            }
            String api = hostIP + uri.toString(); //获取重定向后的打印地址
            HttpHeaders headers = new HttpHeaders();
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_PDF));
            HttpEntity<Object> httpEntity = new HttpEntity<>(headers);
            ResponseEntity<byte[]> labelByte = restTemplate.exchange(api, HttpMethod.GET, httpEntity, byte[].class); //调用打印面单
            String labelFileName = String.format(this.labelNameTemplate, labelFolder, orderId); //图片本地路径
            if (isPdfLabel()) {
                labelUrl = getPdfLabel(labelByte.getBody(), labelFileName);
            } else {
                labelUrl = getImageLabel(labelByte.getBody(), labelFileName);
            }

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return labelUrl;
    }


    private GenerateOrderResponse.Error processFailReply(HuaLeiLogisticsOrderResponse response) {
        String message;
        try {
            message = URLDecoder.decode(response.getMessage(), "utf-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        return buildError(null, message);
    }

    protected boolean isResponseOk(HuaLeiLogisticsOrderResponse reply) {
        return !reply.getAck().equals("false") && StringUtils.hasText(reply.getTrackingNumber());
    }

    @Override
    public void doTrack(TmsPackageEntity tmsPackageEntity) {
        huaLeiTrackService.doTrack(tmsPackageEntity, trackUrl);
    }

    public void validateOrderRequest(OrderNewRequest request) {
        OrderNewInfo orderInfo = request.getOrderInfo();
        super.validateOrderRequest(request);
        Validator.isValid(orderInfo.getReceiver(), Objects::nonNull, "receiver 节点不能为空");
        Validator.isValid(orderInfo.getReceiver().getPostCode(), StringUtils::hasText, "receiver.postCode 节点不能为空");
        orderInfo.getOrderItemInfoList().forEach(item -> {
            Validator.isValid(item.getCount(), Objects::nonNull, "OrderItemInfo.count 不能为空");
            Validator.isValid(item.getCustomsPrice(), Objects::nonNull, "OrderItemInfo.customsPrice 不能为空");
        });
    }

    @Override
    public SecondaryNumberResponse getSecondaryNumber(TmsPackageEntity packageEntity) {
        if (StringUtils.hasText(packageEntity.getSecondaryNumber()) && !packageEntity.getSecondaryNumber().equalsIgnoreCase(packageEntity.getLogisticsNo())) {
            return super.getSecondaryNumber(packageEntity);
        }
        String secondaryNumber = getSecondaryNumByApi(packageEntity);
        packageEntity.setSecondaryNumber(secondaryNumber);
        packageService.save(packageEntity);

        List<TmsAlertTaskEntity> alertTaskList = alertTaskRepository.findByLogisticsNoAndDeleteFlagAndSecondaryNumberIsNull(packageEntity.getLogisticsNo(), TmsPackageEntity.NOT_DELETE);
        if (!alertTaskList.isEmpty()) {
            alertTaskList.stream().forEach(alertTaskEntity -> {
                alertTaskEntity.setSecondaryNumber(secondaryNumber);
                alertTaskService.save(alertTaskEntity);
            });
        }
        return super.getSecondaryNumber(packageEntity);
    }

    public String getSecondaryNumByApi(TmsPackageEntity packageEntity) {
        String api = trackingNumberUrl + "?documentCode={documentCode}";
        TmsRequestLogEntity logEntity = requestLogService.retrieveSecondaryNumberLog(packageEntity);
        String secondaryNumber = "";
        try {
            String response = restTemplate.getForObject(api, String.class, packageEntity.getTmsTid());
            JSONObject jsonObject = JSON.parseObject(response);
            if (!"获取成功".equals(jsonObject.getString("msg"))) {
                requestLogService.updateLog(logEntity, TmsRequestLogEntity.FAIL, String.format("%s?documentCode=%s",
                        trackingNumberUrl, packageEntity.getTmsTid()), response, packageEntity.getLogisticsNo());
                LOGGER.info("订单{}，暂无跟踪号", packageEntity.getTmsTid());
            } else {
                secondaryNumber = jsonObject.getString("order_transfercode");
                requestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS,
                        String.format("%s?documentCode=%s", trackingNumberUrl, packageEntity.getTmsTid()), response, secondaryNumber);
            }
        } catch (Exception e) {
            LOGGER.error(String.format("tmsTid :%s 获取转运单号异常:%s", packageEntity.getTmsTid(), e.getMessage()), e);
            requestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION,
                    String.format("%s?documentCode=%s", trackingNumberUrl, packageEntity.getTmsTid()), e.getMessage(), packageEntity.getLogisticsNo());
        }
        return secondaryNumber;
    }

    @Override
    public PrintLabelResponse printLabel(TmsPackageEntity packageEntity) {
        if (StringUtils.hasText(packageEntity.getLabelUrl())) {
            return super.printLabel(packageEntity);
        }
        String labelUrl = getLabel(packageEntity.getLogisticsTid());
        packageEntity.setLabelUrl(labelUrl);
        packageService.save(packageEntity);
        return super.printLabel(packageEntity);
    }

}
