package com.nsy.api.tms.service.upload;

import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.tms.enumeration.QuartzUploadQueueTypeEnum;
import com.nsy.api.tms.request.upload.UploadRequest;
import com.nsy.api.tms.response.upload.UploadResponse;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ApplicationObjectSupport;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.Map;

@Component
public class ProcessUploadDataServiceFactory extends ApplicationObjectSupport implements InitializingBean {

    private final EnumMap<QuartzUploadQueueTypeEnum, IProcessUploadDataService> processUploadDataServiceMap = new EnumMap<>(QuartzUploadQueueTypeEnum.class);

    public UploadResponse distribution(UploadRequest request) {
        return this.route(request.getTypeEnum()).processUploadData(request);
    }

    public IProcessUploadDataService route(QuartzUploadQueueTypeEnum typeEnum) {
        IProcessUploadDataService processUploadDataService = processUploadDataServiceMap.get(typeEnum);
        if (processUploadDataService == null) {
            throw new BusinessServiceException(String.format("upload queue type does not exist: %s", typeEnum.toString()));
        }
        return processUploadDataService;
    }

    @Override
    public void afterPropertiesSet() {
        ApplicationContext applicationContext = this.getApplicationContext();
        assert applicationContext != null;
        Map<String, IProcessUploadDataService> factories = applicationContext.getBeansOfType(IProcessUploadDataService.class);
        for (IProcessUploadDataService processUploadDataService : factories.values()) {
            processUploadDataServiceMap.put(processUploadDataService.type(), processUploadDataService);
        }
    }
}
