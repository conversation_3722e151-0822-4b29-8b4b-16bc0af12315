package com.nsy.api.tms.service.external.refactor;

import cn.hutool.core.util.StrUtil;
import com.nsy.api.core.apicore.exception.BusinessServiceException;
import com.nsy.api.core.apicore.util.MD5Util;
import com.nsy.api.tms.annotation.LogisticsServiceHandler;
import com.nsy.api.tms.constants.LogisticsChannelConstant;
import com.nsy.api.tms.dao.entity.TmsLogisticsAccountEntity;
import com.nsy.api.tms.dao.entity.TmsLogisticsChannelConfigEntity;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsRequestLogEntity;
import com.nsy.api.tms.domain.OrderItemInfo;
import com.nsy.api.tms.domain.order.OrderNewInfo;
import com.nsy.api.tms.enumeration.LogisticsMethodEnum;
import com.nsy.api.tms.enumeration.PackageStatusEnum;
import com.nsy.api.tms.logistics.dmxsmart.DmxsmartBaseResponse;
import com.nsy.api.tms.logistics.dmxsmart.request.DmxsmartReviceInfo;
import com.nsy.api.tms.logistics.dmxsmart.request.DmxsmartStockInOrderCreateRequest;
import com.nsy.api.tms.logistics.dmxsmart.request.DmxsmartStockInOrderDetail;
import com.nsy.api.tms.logistics.dmxsmart.request.DmxsmartStockInOrderSearchRequest;
import com.nsy.api.tms.logistics.dmxsmart.request.DmxsmartStockOutProductDetail;
import com.nsy.api.tms.logistics.dmxsmart.request.DmxsmartStockoutOrderCancelRequest;
import com.nsy.api.tms.logistics.dmxsmart.request.DmxsmartStockoutOrderCreateRequest;
import com.nsy.api.tms.logistics.dmxsmart.request.DmxsmartStockoutOrderSearchRequest;
import com.nsy.api.tms.logistics.dmxsmart.response.DmxsmartStockInOrderCreateResponse;
import com.nsy.api.tms.logistics.dmxsmart.response.DmxsmartStockInOrderLabelResponse;
import com.nsy.api.tms.logistics.dmxsmart.response.DmxsmartStockInOrderProductInfoResponse;
import com.nsy.api.tms.logistics.dmxsmart.response.DmxsmartStockInOrderSearchResponse;
import com.nsy.api.tms.logistics.dmxsmart.response.DmxsmartStockoutOrderCreateResponse;
import com.nsy.api.tms.logistics.dmxsmart.response.DmxsmartStockoutOrderSearchResponse;
import com.nsy.api.tms.repository.TmsLogisticsChannelConfigRepository;
import com.nsy.api.tms.request.OrderNewRequest;
import com.nsy.api.tms.response.GenerateOrderResponse;
import com.nsy.api.tms.response.SecondaryNumberResponse;
import com.nsy.api.tms.utils.JsonMapper;
import com.nsy.api.tms.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/7/31 14:11
 */
@Service
@LogisticsServiceHandler(logisticsMethod = LogisticsMethodEnum.AFM)
public class DmxsmartNewService extends BaseLogisticsNewService implements InitializingBean {

    @Autowired
    private TmsLogisticsChannelConfigRepository channelConfigRepository;

    private static final Logger LOGGER = LoggerFactory.getLogger(DmxsmartNewService.class);

    public static final String APP_ID = "Dmxsmart-app-id";
    public static final String APP_KEY = "Dmxsmart-app-key";
    public static final String APP_SECRET = "Dmxsmart-app-secert";
    public static final String SPACE_CODE = "Dmxsmart-space-code";
    public static final String SERVICE_CODE_OZ = "Dmxsmart-service-code-oz";
    public static final String SERVICE_CODE = "Dmxsmart-service-code";

    @Value("${dmxsmart.server.url}")
    private String dmxsmartUrl;


    @Override
    protected DmxsmartStockoutOrderCreateRequest buildLogisticsOrderRequest(OrderNewRequest orderRequest, Map<String, String> configMap) {
        DmxsmartStockoutOrderCreateRequest request = new DmxsmartStockoutOrderCreateRequest();
        OrderNewInfo orderInfo = orderRequest.getOrderInfo();
        double weightTotal = orderInfo.getOrderItemInfoList().stream().filter(detail -> Objects.nonNull(detail.getWeight())).mapToDouble(OrderItemInfo::getWeight).sum();
        //重量为空或者小于1磅
        if (ObjectUtils.isEmpty(weightTotal) || weightTotal < 0.43) {
            request.setServiceCode(configMap.get(SERVICE_CODE_OZ));
        } else {
            request.setServiceCode(configMap.get(SERVICE_CODE));
        }
        request.setWarehouseCode(configMap.get(SPACE_CODE));
        request.setReferenceId(orderInfo.getBusinessKey());
        request.setCombinePackage(Boolean.TRUE);
        DmxsmartReviceInfo consignee = new DmxsmartReviceInfo();
        consignee.setName(orderInfo.getReceiver().getName());
        consignee.setPhone(StringUtils.isNotBlank(orderInfo.getReceiver().getPhone()) ? orderInfo.getReceiver().getPhone() : orderInfo.getReceiver().getMobile());
        consignee.setEmail(orderInfo.getReceiver().getEmail());
        consignee.setState(orderInfo.getReceiver().getProvince());
        consignee.setZip(orderInfo.getReceiver().getPostCode());
        consignee.setCity(orderInfo.getReceiver().getCity());
        consignee.setCounty(orderInfo.getReceiver().getCounty());
        consignee.setCountryCode(orderInfo.getReceiver().getCountry());
        if (orderInfo.getReceiver().getStreet().length() <= 50) {
            consignee.setAddressLine1(StringUtils.substring(orderInfo.getReceiver().getStreet(), 0, 50));
        } else if (orderInfo.getReceiver().getStreet().length() > 50 && orderInfo.getReceiver().getStreet().length() <= 100) {
            consignee.setAddressLine1(StringUtils.substring(orderInfo.getReceiver().getStreet(), 0, 50));
            consignee.setAddressLine2(StringUtils.substring(orderInfo.getReceiver().getStreet(), 50, 100));
        } else if (orderInfo.getReceiver().getStreet().length() > 100) {
            throw new BusinessServiceException("收件人地址不能大于100个字符");
        }
        request.setConsignee(consignee);
        List<DmxsmartStockOutProductDetail> orderItemList = new ArrayList<>();
        orderInfo.getOrderItemInfoList().forEach(detail -> {
            DmxsmartStockOutProductDetail productDetail = new DmxsmartStockOutProductDetail();
            productDetail.setQty(detail.getCount());
            productDetail.setSku(detail.getSku());
            orderItemList.add(productDetail);
        });
        request.setOrderItems(orderItemList);
        return request;
    }

    @Override
    protected String buildSerializedRequest(OrderNewRequest request, TmsLogisticsAccountEntity logisticsAccountEntity) {
        Map<String, String> configMap = buildAccountConfig(logisticsAccountEntity, request.getOrderInfo());
        DmxsmartStockoutOrderCreateRequest request1 = buildLogisticsOrderRequest(request, configMap);
        return JsonMapper.toJson(request1);
    }

    @Override
    public void afterPropertiesSet() throws Exception {

    }

    @Override
    public GenerateOrderResponse doRequest(OrderNewRequest orderRequest, Map<String, String> configMap, String requestContent) {
        TmsRequestLogEntity logEntity = requestLogService.recordBaseInfoLog(orderRequest.getOrderInfo());
        GenerateOrderResponse response = new GenerateOrderResponse();
        DmxsmartStockoutOrderCreateResponse dmxsmartResponse = null;
        try {
            dmxsmartResponse = postToDmxsmart(requestContent, configMap, "wms.order.create", DmxsmartStockoutOrderCreateResponse.class);
            GenerateOrderResponse.SuccessEntity successEntity = buildSuccessEntity(orderRequest.getOrderInfo(), dmxsmartResponse.getTrackingNos().get(0), "wms.order.create", dmxsmartResponse.getOrderId(), null);
            response.setSuccessEntity(successEntity);
            requestLogService.updateLog(logEntity, TmsRequestLogEntity.SUCCESS, requestContent, JsonMapper.toJson(dmxsmartResponse), successEntity.getLogisticsNo());

        } catch (Exception e) {
            requestLogService.updateLog(logEntity, TmsRequestLogEntity.EXCEPTION, requestContent, dmxsmartResponse == null ? e.getMessage() : JsonMapper.toJson(dmxsmartResponse), "");
            LOGGER.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        return response;
    }

    @Override
    public SecondaryNumberResponse getSecondaryNumber(TmsPackageEntity packageEntity) {
        if (StringUtils.isNotBlank(packageEntity.getSecondaryNumber()) && !PackageStatusEnum.CREATED.getDesc().equalsIgnoreCase(packageEntity.getStatus())) {
            return super.getSecondaryNumber(packageEntity);
        }
        Map<String, String> configMap;
        if (StrUtil.isNotBlank(packageEntity.getKeyGroup())) {
            configMap = configService.getConfigMap(packageEntity.getKeyGroup());
        } else {
            LOGGER.error("海外仓获取配置异常：使用旧账号配置获取海外仓订单！");
            configMap = logisticsAccountService.buildAccountConfig(packageEntity.getLogisticsAccountId());
        }
        DmxsmartStockoutOrderSearchRequest request = new DmxsmartStockoutOrderSearchRequest();
        request.setReferenceId(packageEntity.getBusinessKey());
        DmxsmartStockoutOrderSearchResponse dmxsmartResponse = postToDmxsmart(JsonMapper.toJson(request), configMap, "wms.order.get", DmxsmartStockoutOrderSearchResponse.class);
        if (dmxsmartResponse == null) {
            throw new RuntimeException("AFM仓查询订单失败：" + packageEntity.getLogisticsNo());
        }
        if (Objects.nonNull(dmxsmartResponse.getStatus()) && 7 == dmxsmartResponse.getStatus() && PackageStatusEnum.CREATED.getDesc().equals(packageEntity.getStatus())) {
            packageEntity.setStatus(PackageStatusEnum.SHIPPED.getDesc());
            packageService.save(packageEntity);
        }
        return super.getSecondaryNumber(packageEntity);
    }

    private <T> T postToDmxsmart(String requestContent, Map<String, String> configMap, String method, Class<T> responseClass) {
        HttpHeaders headers = new HttpHeaders();
        String timestamp = String.valueOf(System.currentTimeMillis());
        headers.add("appId", configMap.get(APP_ID));
        headers.add("appKey", configMap.get(APP_KEY));
        headers.add("language", "cn");
        headers.add("method", method);
        headers.add("timestamp", timestamp);
        headers.add("sign", this.sign(timestamp, method, configMap, requestContent));
        headers.add("Content-Type", "application/json;charset=UTF-8");
        HttpEntity<String> entity = new HttpEntity<>(requestContent, headers);
        LOGGER.info("大迈未来系统请求接口类型为：{},请求参数为：{}", method, JsonMapper.toJson(requestContent));
        ResponseEntity<DmxsmartBaseResponse> responseEntity = this.restTemplate.postForEntity(dmxsmartUrl, entity, DmxsmartBaseResponse.class);
        LOGGER.info("大迈未来系统请求接口类型为：{},返回参数为：{}", method, JsonMapper.toJson(responseEntity.getBody()));
        if (HttpStatus.OK != responseEntity.getStatusCode() || responseEntity.getBody() == null) {
            // http失败
            throw new RuntimeException(String.format("responseEntity=%s", JsonMapper.toJson(responseEntity)));
        }
        if (!responseEntity.getBody().getSuccess()) {
            // api内部失败
            throw new BusinessServiceException(responseEntity.getBody().getErrorMessage());
        }
        if (null == responseClass || Void.class.equals(responseClass)) {
            return null;
        }
        return Optional.ofNullable(responseEntity.getBody())
                .map(DmxsmartBaseResponse::getData)
                .map(data -> {
                    String responseText = JsonMapper.toJson(data);
                    LOGGER.debug("大迈未来系统请求接口返回信息 responseText={}", responseText);
                    return JsonMapper.fromJson(responseText, responseClass);
                }).orElse(null);
    }


    public DmxsmartStockInOrderCreateResponse createStockInOrder(String orderNo, List<DmxsmartStockInOrderDetail> stockInOrderDetailList) {
        Map<String, String> configMap = configService.getConfigMap(stockInOrderDetailList.get(0).getSpaceName());
        DmxsmartStockInOrderCreateRequest stockinOrderRequest = new DmxsmartStockInOrderCreateRequest();
        stockinOrderRequest.setReferenceId(orderNo);
        stockinOrderRequest.setWarehouseCode(configMap.get(SPACE_CODE));
        stockinOrderRequest.setHandleMethod("pick_pack");
        stockinOrderRequest.setDimensionUnit("cm");
        stockinOrderRequest.setWeightUnit("kg");
        stockinOrderRequest.setBoxs(stockInOrderDetailList);
        DmxsmartStockInOrderCreateResponse response = this.postToDmxsmart(JsonMapper.toJson(stockinOrderRequest), configMap, "wms.inbound.create", DmxsmartStockInOrderCreateResponse.class);
        LOGGER.info("大迈未来系统返回结果为：{}", JsonMapper.toJson(response));
        return response;
    }

    public List<DmxsmartStockInOrderProductInfoResponse> getStockInOrderInfo(String platformReferenceNo, String spaceName) {
        DmxsmartStockInOrderSearchRequest stockoutOrderRequest = new DmxsmartStockInOrderSearchRequest();
        stockoutOrderRequest.setInboundId(platformReferenceNo);
        Map<String, String> configMap = configService.getConfigMap(spaceName);
        DmxsmartStockInOrderSearchResponse response = this.postToDmxsmart(JsonMapper.toJson(stockoutOrderRequest), configMap, "wms.inbound.get", DmxsmartStockInOrderSearchResponse.class);
        if (ObjectUtils.isEmpty(response) || Objects.isNull(response.getStatus()) || !Lists.newArrayList(5, 6).contains(response.getStatus())) {
            LOGGER.info("大迈未来系统查询入库单返回为空或状态不为已入库/已上架：{}", JsonMapper.toJson(response));
            return Collections.emptyList();
        }
        return response.getProducts();
    }

    public DmxsmartStockInOrderLabelResponse getLabel(String platformReferenceNo, String spaceName) {
        DmxsmartStockInOrderSearchRequest stockoutOrderRequest = new DmxsmartStockInOrderSearchRequest();
        stockoutOrderRequest.setInboundId(platformReferenceNo);
        Map<String, String> configMap = configService.getConfigMap(spaceName);
        return this.postToDmxsmart(JsonMapper.toJson(stockoutOrderRequest), configMap, "wms.inbound.getLabel", DmxsmartStockInOrderLabelResponse.class);
    }

    public Map<String, String> getSelfConfigMap() {
        TmsLogisticsChannelConfigEntity channelConfigEntity = channelConfigRepository.findByLogisticsChannelName(LogisticsChannelConstant.AFM);
        List<TmsLogisticsAccountEntity> logisticsAccountEntityList = logisticsAccountRepository.findByLogisticsCompany(channelConfigEntity.getLogisticsCompany());
        if (CollectionUtils.isEmpty(logisticsAccountEntityList)) {
            throw new BusinessServiceException("未配置物流账号");
        }
        return logisticsAccountService.buildAccountConfig(logisticsAccountEntityList.get(0).getId());
    }

    @Override
    public void cancelOrder(TmsPackageEntity packageEntity) {
        if (!PackageStatusEnum.CREATED.getDesc().equalsIgnoreCase(packageEntity.getStatus())) {
            return;
        }
        Map<String, String> configMap;
        if (StrUtil.isNotBlank(packageEntity.getKeyGroup())) {
            configMap = configService.getConfigMap(packageEntity.getKeyGroup());
        } else {
            LOGGER.error("海外仓获取配置异常：使用旧账号配置获取海外仓订单！");
            configMap = logisticsAccountService.buildAccountConfig(packageEntity.getLogisticsAccountId());
        }
        DmxsmartStockoutOrderCancelRequest request = new DmxsmartStockoutOrderCancelRequest();
        request.setReferenceId(packageEntity.getBusinessKey());
        this.postToDmxsmart(JsonMapper.toJson(request), configMap, "wms.order.cancel", Void.class);
        packageEntity.setStatus(PackageStatusEnum.CANCEL.getDesc());
        packageService.save(packageEntity);
    }

    public String sign(String timestamp, String method, Map<String, String> configMap, String requestContent) {
        String encryStr = String.format("appId=%s&appKey=%s&language=cn&method=%s&timestamp=%s%s%s",
                configMap.get(APP_ID), configMap.get(APP_KEY), method, timestamp, requestContent, configMap.get(APP_SECRET));
        return MD5Util.crypt(encryStr);
    }
}
