package com.nsy.api.tms.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.tms.constants.CountryCodeConstant;
import com.nsy.api.tms.constants.LogisticsChannelConstant;
import com.nsy.api.tms.dao.entity.BdSpaceAreaPostCodeMappingEntity;
import com.nsy.api.tms.dao.entity.LogisticsOptimizationRecordEntity;
import com.nsy.api.tms.dao.entity.LogisticsOptimizationRuleCompanyMappingEntity;
import com.nsy.api.tms.dao.entity.LogisticsOptimizationRuleEntity;
import com.nsy.api.tms.external.wms.WmsApiService;
import com.nsy.api.tms.external.wms.response.BdSpace;
import com.nsy.api.tms.mapper.LogisticsOptimizationRuleCompanyMappingMapper;
import com.nsy.api.tms.request.LogisticsCompanyMatchRequest;
import com.nsy.api.tms.response.LogisticsCompanyOrderMatchResponse;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单物流规则映射配置(OrderMatchRuleCompanyMapping)服务层
 *
 * <AUTHOR>
 * @since 2023-04-06 17:24:51
 */
@Service
public class LogisticsOptimizationRuleCompanyMappingService extends ServiceImpl<LogisticsOptimizationRuleCompanyMappingMapper, LogisticsOptimizationRuleCompanyMappingEntity> {

    @Resource
    LogisticsOptimizationRuleService ruleConfigService;
    @Resource
    LogisticsOptimizationRecordService companyRecordService;
    @Resource
    private BdSpaceAreaPostCodeMappingService areaPostCodeMappingService;
    @Resource
    private WmsApiService wmsApiService;
    @Resource
    private TmsExtendedAreaSurchargeService extendedAreaSurchargeService;

    /**
     * 订单根据物流配置，返回给erp物流公司
     * 1、根据重量匹配
     * 2、根据地区、带电等匹配(目前不支持)
     * <AUTHOR>
     * 2023-04-06
     */
    public LogisticsCompanyOrderMatchResponse orderMatchCompany(LogisticsCompanyMatchRequest request) {
        LogisticsOptimizationRecordEntity recordEntity = companyRecordService.addRecord(request);
        LogisticsCompanyOrderMatchResponse response = new LogisticsCompanyOrderMatchResponse();
        // 固定规则
        if (StrUtil.equalsAnyIgnoreCase(request.getCountryCode(), CountryCodeConstant.US) && StrUtil.contains(request.getSpaceName(), "谷仓")) {
            BdSpace spaceInfo = wmsApiService.getSpaceByName(request.getSpaceName(), request.getLocation());
            if (spaceInfo != null && StrUtil.contains(spaceInfo.getDescription(), "谷仓") && StrUtil.isNotBlank(request.getPostCode())) {
                // 是谷仓的仓库
                BdSpaceAreaPostCodeMappingEntity mappingEntity = areaPostCodeMappingService.findBySpaceIdAndPostCode(spaceInfo.getSpaceId(), request.getPostCode());
                return getLogisticsCompanyOrderMatchResponse(request, mappingEntity, response, recordEntity);
            }
        }

    // 找出适配的规则
        List<LogisticsOptimizationRuleEntity> ruleList = ruleConfigService.getConfigByReq(request);
        if (CollectionUtils.isEmpty(ruleList)) {
            response.setErrorMsg("未配置具体的订单匹配规则！");
            companyRecordService.recordMessage(recordEntity, response.getErrorMsg());
            return response;
        }
        // 找出规则对应的物流公司
        List<LogisticsOptimizationRuleCompanyMappingEntity> list = getConfigByReq(ruleList);
        if (CollectionUtils.isEmpty(list)) {
            response.setErrorMsg("规则未配置对应的物流公司！");
            companyRecordService.recordMessage(recordEntity, response.getErrorMsg());
            return response;
        }
        List<Integer> mappingIds = list.stream().map(LogisticsOptimizationRuleCompanyMappingEntity::getId).collect(Collectors.toList());
        // 公司对应的storeId记录统计
        Integer recordMappingId = companyRecordService.getMinRecordCountByStoreIdToday(request.getStoreId(), mappingIds);
        if (recordMappingId == null) {
            // 今天没有匹配记录
            recordMappingId = mappingIds.get(0);
        }
        LogisticsOptimizationRuleCompanyMappingEntity byId = getById(recordMappingId);
        if (byId == null) {
            response.setErrorMsg(recordMappingId + "找不到映射关系！");
            companyRecordService.recordMessage(recordEntity, response.getErrorMsg());
            return response;
        }
        response.setLogisticsCompany(byId.getLogisticsCompany());
        companyRecordService.successRecord(recordMappingId, recordEntity, request);
        return response;
    }

    /**
     *  1、找对应的BdSpaceAreaPostCodeMappingEntity，如果有找到，再判断AreaName是不是在 1-8 如果是就走LogisticsChannelConstant.GOODCANG_GC_PARCEL
     *  2、如果AreaName = 9-15 或者没找到BdSpaceAreaPostCodeMappingEntity 选择物流LogisticsChannelConstant.GOODCANG_USPS_QZ
     *
     * 3、如果选择了物流LogisticsChannelConstant.GOODCANG_GC_PARCEL，那么还要判断偏远分区表（tms_extended_area_surcharge）的level是不是“B”和“C”偏远
     *    如果是，那要选择物流LogisticsChannelConstant.GOODCANG_USPS_QZ
     */
    private LogisticsCompanyOrderMatchResponse getLogisticsCompanyOrderMatchResponse(LogisticsCompanyMatchRequest request, BdSpaceAreaPostCodeMappingEntity mappingEntity, LogisticsCompanyOrderMatchResponse response, LogisticsOptimizationRecordEntity recordEntity) {
        String selectedLogisticsCompany = LogisticsChannelConstant.GOODCANG_USPS_QZ; // 默认选择USPS
        // 第一步：根据BdSpaceAreaPostCodeMappingEntity判断AreaName
        if (mappingEntity != null && StrUtil.isNotBlank(mappingEntity.getAreaName())) {
            try {
                // 尝试解析AreaName为数字
                int areaNameValue = Integer.parseInt(mappingEntity.getAreaName().trim());
                // 如果AreaName在1-8范围内，选择GC_PARCEL
                if (areaNameValue >= 1 && areaNameValue <= 8) {
                    selectedLogisticsCompany = LogisticsChannelConstant.GOODCANG_GC_PARCEL;
                }
                // 如果AreaName在9-15范围内，选择USPS_QZ（已经是默认值）
            } catch (NumberFormatException e) {
                // 如果AreaName不是数字，记录日志并使用默认值USPS_QZ
                companyRecordService.recordMessage(recordEntity, "AreaName不是有效数字: " + mappingEntity.getAreaName());
            }
        }

        // 第二步：如果选择了GC_PARCEL，需要检查偏远分区
        if (LogisticsChannelConstant.GOODCANG_GC_PARCEL.equals(selectedLogisticsCompany)
                && StrUtil.isNotBlank(request.getPostCode())
                && StrUtil.isNotBlank(request.getCountryCode())) {
            // 查询偏远分区等级
            String remoteLevel = extendedAreaSurchargeService.findByLogisticsCompanyAndCountryCodeAndPostalCode(
                LogisticsChannelConstant.GOODCANG_GC_PARCEL,
                request.getCountryCode(),
                request.getPostCode()
            );

            // 如果偏远等级是B或C，改为选择USPS_QZ
            if (StrUtil.equalsAnyIgnoreCase(remoteLevel, "B", "C")) {
                selectedLogisticsCompany = LogisticsChannelConstant.GOODCANG_USPS_QZ;
                companyRecordService.recordMessage(recordEntity,
                    String.format("由于偏远等级为%s，从GC_PARCEL改为USPS_QZ", remoteLevel));
            }
        }

        // 设置最终选择的物流公司
        response.setLogisticsCompany(selectedLogisticsCompany);
        return response;
    }

//    private void setByWeight(LogisticsCompanyMatchRequest request, LogisticsCompanyOrderMatchResponse response) {
//        if (request.getPackageWeight().compareTo(new BigDecimal(336)) <= 0) {
//            response.setLogisticsCompany(LogisticsChannelConstant.GOODCANG_USPS_QZ);
//        } else if (request.getPackageWeight().compareTo(new BigDecimal(2250)) <= 0) {
//            response.setLogisticsCompany(LogisticsChannelConstant.GOODCANG_UPS_QZ);
//        } else {
//            response.setLogisticsCompany(LogisticsChannelConstant.GOODCANG_USPS_QZ);
//        }
//    }

    public List<LogisticsOptimizationRuleCompanyMappingEntity> getConfigByReq(List<LogisticsOptimizationRuleEntity> ruleList) {
        LambdaQueryWrapper<LogisticsOptimizationRuleCompanyMappingEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(LogisticsOptimizationRuleCompanyMappingEntity::getRuleConfigId, ruleList.stream().map(LogisticsOptimizationRuleEntity::getId).collect(Collectors.toList()));
        return list(wrapper);
    }
}
