package com.nsy.api.tms.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.dao.entity.TmsPackageLogEntity;
import com.nsy.api.tms.enumeration.PackageLogEnum;
import com.nsy.api.tms.mapper.PackageLogMapper;
import com.nsy.api.tms.service.privilege.AccessControlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PackageLogService extends ServiceImpl<PackageLogMapper, TmsPackageLogEntity> {

    @Autowired
    AccessControlService accessControlService;


    public void addLog(TmsPackageEntity entity, PackageLogEnum logEnum, String realName, String ipAddress) {
        TmsPackageLogEntity logEntity = new TmsPackageLogEntity();
        logEntity.setPackageId(entity.getId());
        logEntity.setLogisticsNo(entity.getLogisticsNo());
        logEntity.setLogType(logEnum.getDesc());
        if (logEnum == PackageLogEnum.UPDATE_STATUS) {
            logEntity.setContent("物流单号:" + entity.getLogisticsNo() + "， 更新状态" + entity.getStatus());
        }
        if (logEnum == PackageLogEnum.MANUAL_TRACKING) {
            logEntity.setContent("物流单号:" + entity.getLogisticsNo() + "， 手动获取物流");
        }
        if (logEnum == PackageLogEnum.UPDATE_SOLUTION) {
            logEntity.setContent("物流单号:" + entity.getLogisticsNo() + "， 更新解决方案");
        }
        if (logEnum == PackageLogEnum.EXCEPTION_COMPLETE) {
            logEntity.setContent("物流单号:" + entity.getLogisticsNo() + "， 异常处理完成");
        }
        if (logEnum == PackageLogEnum.EXCEPTION_RE_DELIVERY) {
            logEntity.setContent("物流单号:" + entity.getLogisticsNo() + "， 用原单号补发，现已重新发出");
        }
        if (logEnum == PackageLogEnum.DELETE_PACKAGE) {
            logEntity.setContent("重新获取物流单号，此物流单号:" + entity.getLogisticsNo() + "被删除");
        }
        logEntity.setCreateBy(realName);
        logEntity.setIpAddress(ipAddress);
        save(logEntity);
    }

    public void addLog(TmsPackageEntity entity, String logType, String logContent, String realName, String ipAddress) {
        TmsPackageLogEntity logEntity = new TmsPackageLogEntity();
        logEntity.setPackageId(entity.getId());
        logEntity.setLogisticsNo(entity.getLogisticsNo());
        logEntity.setLogType(logType);
        logEntity.setContent(StrUtil.maxLength(logContent, 300));
        logEntity.setCreateBy(realName);
        logEntity.setIpAddress(ipAddress);
        save(logEntity);
    }
}
