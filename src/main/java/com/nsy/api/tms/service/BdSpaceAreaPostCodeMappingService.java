package com.nsy.api.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.nsy.api.tms.dao.entity.BdSpaceAreaPostCodeMappingEntity;
import com.nsy.api.tms.mapper.BdSpaceAreaPostCodeMappingMapper;
import com.nsy.api.tms.request.BdSpaceAreaPostCodeMappingInsertRequest;
import com.nsy.api.tms.request.BdSpaceAreaPostCodeMappingPageRequest;
import com.nsy.api.tms.response.BdSpaceAreaPostCodeMappingResponse;
import com.nsy.api.tms.response.PageResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 仓库地区邮编映射服务类
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class BdSpaceAreaPostCodeMappingService extends ServiceImpl<BdSpaceAreaPostCodeMappingMapper, BdSpaceAreaPostCodeMappingEntity> {


    /**
     * 分页查询映射关系
     */
    public PageResponse<BdSpaceAreaPostCodeMappingResponse> queryByPage(BdSpaceAreaPostCodeMappingPageRequest pageRequest) {
        Page<BdSpaceAreaPostCodeMappingEntity> page = new Page<>(pageRequest.getPageIndex(), pageRequest.getPageSize());
        LambdaQueryWrapper<BdSpaceAreaPostCodeMappingEntity> queryWrapper = new LambdaQueryWrapper<>();

        if (pageRequest.getSpaceId() != null) {
            queryWrapper.eq(BdSpaceAreaPostCodeMappingEntity::getSpaceId, pageRequest.getSpaceId());
        }
        if (pageRequest.getSpaceName() != null && !pageRequest.getSpaceName().trim().isEmpty()) {
            queryWrapper.like(BdSpaceAreaPostCodeMappingEntity::getSpaceName, pageRequest.getSpaceName() + "%");
        }
        if (pageRequest.getPostCode() != null && !pageRequest.getPostCode().trim().isEmpty()) {
            queryWrapper.like(BdSpaceAreaPostCodeMappingEntity::getPostCode, pageRequest.getPostCode() + "%");
        }
        if (pageRequest.getAreaName() != null && !pageRequest.getAreaName().trim().isEmpty()) {
            queryWrapper.like(BdSpaceAreaPostCodeMappingEntity::getAreaName, pageRequest.getAreaName() + "%");
        }

        queryWrapper.orderByDesc(BdSpaceAreaPostCodeMappingEntity::getId);

        IPage<BdSpaceAreaPostCodeMappingEntity> result = this.page(page, queryWrapper);

        // 转换为Response对象
        List<BdSpaceAreaPostCodeMappingResponse> responseList = result.getRecords().stream().map(this::convertToResponse).collect(Collectors.toList());

        PageResponse<BdSpaceAreaPostCodeMappingResponse> pageResponse = new PageResponse<>();
        pageResponse.setContent(responseList);
        pageResponse.setTotalCount(result.getTotal());
        return pageResponse;
    }

    /**
     * 保存或编辑映射关系
     */
    @Transactional
    public void saveOrEdit(BdSpaceAreaPostCodeMappingInsertRequest request) {
        BdSpaceAreaPostCodeMappingEntity entity = new BdSpaceAreaPostCodeMappingEntity();
        BeanUtils.copyProperties(request, entity);

        if (request.getId() != null) {
            // 编辑模式
            this.updateMapping(entity);
        } else {
            // 新增模式
            this.createMapping(entity);
        }
    }

    /**
     * 根据ID获取详情
     */
    public BdSpaceAreaPostCodeMappingResponse getOneById(Integer id) {
        BdSpaceAreaPostCodeMappingEntity entity = this.getById(id);
        if (entity == null) {
            throw new RuntimeException("映射关系不存在");
        }
        return convertToResponse(entity);
    }

    /**
     * 实体转响应对象
     */
    private BdSpaceAreaPostCodeMappingResponse convertToResponse(BdSpaceAreaPostCodeMappingEntity entity) {
        BdSpaceAreaPostCodeMappingResponse response = new BdSpaceAreaPostCodeMappingResponse();
        BeanUtils.copyProperties(entity, response);
        return response;
    }

    /**
     * 根据仓库ID查找映射关系
     */
    public List<BdSpaceAreaPostCodeMappingEntity> findBySpaceId(Integer spaceId) {
        LambdaQueryWrapper<BdSpaceAreaPostCodeMappingEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BdSpaceAreaPostCodeMappingEntity::getSpaceId, spaceId);
        return this.list(queryWrapper);
    }

    /**
     * 根据邮编查找映射关系
     */
    public List<BdSpaceAreaPostCodeMappingEntity> findByPostCode(String postCode) {
        LambdaQueryWrapper<BdSpaceAreaPostCodeMappingEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BdSpaceAreaPostCodeMappingEntity::getPostCode, postCode);
        return this.list(queryWrapper);
    }

    /**
     * 根据仓库ID和邮编查找唯一映射关系
     */
    public BdSpaceAreaPostCodeMappingEntity findBySpaceIdAndPostCode(Integer spaceId, String postCode) {
        LambdaQueryWrapper<BdSpaceAreaPostCodeMappingEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BdSpaceAreaPostCodeMappingEntity::getSpaceId, spaceId);
        queryWrapper.eq(BdSpaceAreaPostCodeMappingEntity::getPostCode, postCode);
        return this.getOne(queryWrapper);
    }

    /**
     * 创建映射关系
     */
    @Transactional
    public BdSpaceAreaPostCodeMappingEntity createMapping(BdSpaceAreaPostCodeMappingEntity mapping) {
        // 检查是否已存在相同的映射关系
        BdSpaceAreaPostCodeMappingEntity existMapping = this.findBySpaceIdAndPostCode(mapping.getSpaceId(), mapping.getPostCode());
        if (existMapping != null) {
            throw new RuntimeException("该仓库和邮编的映射关系已存在");
        }
        this.save(mapping);
        return mapping;
    }

    /**
     * 更新映射关系
     */
    @Transactional
    public BdSpaceAreaPostCodeMappingEntity updateMapping(BdSpaceAreaPostCodeMappingEntity mapping) {
        BdSpaceAreaPostCodeMappingEntity existMapping = this.getById(mapping.getId());
        if (existMapping == null) {
            throw new RuntimeException("映射关系不存在");
        }

        // 检查是否与其他映射关系冲突（除了自己）
        BdSpaceAreaPostCodeMappingEntity conflictMapping = this.findBySpaceIdAndPostCode(mapping.getSpaceId(), mapping.getPostCode());
        if (conflictMapping != null && !conflictMapping.getId().equals(mapping.getId())) {
            throw new RuntimeException("该仓库和邮编的映射关系已存在");
        }

        this.updateById(mapping);
        return mapping;
    }

    /**
     * 删除映射关系
     */
    @Transactional
    public boolean deleteMapping(Integer id) {
        BdSpaceAreaPostCodeMappingEntity existMapping = this.getById(id);
        if (existMapping == null) {
            throw new RuntimeException("映射关系不存在");
        }

        return this.removeById(id);
    }

    /**
     * 批量删除映射关系
     */
    @Transactional
    public boolean deleteMappings(List<Integer> ids) {
        return this.removeByIds(ids);
    }

    /**
     * 根据条件查询映射关系列表
     */
    public List<BdSpaceAreaPostCodeMappingEntity> queryList(Integer spaceId, String spaceName, String postCode, String areaName) {
        LambdaQueryWrapper<BdSpaceAreaPostCodeMappingEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (spaceId != null) {
            queryWrapper.eq(BdSpaceAreaPostCodeMappingEntity::getSpaceId, spaceId);
        }
        if (spaceName != null && !spaceName.trim().isEmpty()) {
            queryWrapper.like(BdSpaceAreaPostCodeMappingEntity::getSpaceName, spaceName);
        }
        if (postCode != null && !postCode.trim().isEmpty()) {
            queryWrapper.like(BdSpaceAreaPostCodeMappingEntity::getPostCode, postCode);
        }
        if (areaName != null && !areaName.trim().isEmpty()) {
            queryWrapper.like(BdSpaceAreaPostCodeMappingEntity::getAreaName, areaName);
        }

        queryWrapper.orderByDesc(BdSpaceAreaPostCodeMappingEntity::getId);
        return this.list(queryWrapper);
    }
}
