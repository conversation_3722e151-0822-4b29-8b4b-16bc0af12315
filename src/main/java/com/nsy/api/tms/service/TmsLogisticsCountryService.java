package com.nsy.api.tms.service;

import com.nsy.api.tms.dao.entity.TmsLogisticsCountryEntity;
import com.nsy.api.tms.dao.entity.TmsLogisticsCountryProvinceMappingEntity;
import com.nsy.api.tms.domain.TmsLogisticsCountryProvinceMapping;
import com.nsy.api.tms.domain.shared.SelectStringModel;
import com.nsy.api.tms.repository.TmsLogisticsCountryProvinceMappingRepository;
import com.nsy.api.tms.repository.TmsLogisticsCountryRepository;
import com.nsy.api.tms.request.TmsLogisticsCountryProvinceListRequest;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.inject.Inject;
import java.util.List;
import java.util.stream.Collectors;


/**
* <AUTHOR>
* @since 1.0
*/
@Service
public class TmsLogisticsCountryService {


    @Inject
    TmsLogisticsCountryRepository logisticsCountryRepository;
    @Autowired
    TmsLogisticsCountryProvinceMappingRepository logisticsCountryProvinceMappingRepository;

    /**
     * 物流使用国家下拉框(国家二字码-国家英文名称)
     * @param logisticsCompany
     * @return
     */
    public List<SelectStringModel> getLogisticsCountrySelect(String logisticsCompany) {
        List<TmsLogisticsCountryEntity> logisticsCountryEntityList = logisticsCountryRepository.findByLogisticsCompany(logisticsCompany);
        if (CollectionUtils.isEmpty(logisticsCountryEntityList)) {
            logisticsCountryEntityList = logisticsCountryRepository.findAll().stream().filter(t -> !StringUtils.hasText(t.getLogisticsCompany())).collect(Collectors.toList());
        }
        return logisticsCountryEntityList.stream().map(entity -> new SelectStringModel(entity.getCountryCode(), entity.getEnglishName())).collect(Collectors.toList());
    }

    /**
     * 物流特定国家的州二字码下拉框(州二字码-州名称)
     * @param request
     * @return
     */
    public List<SelectStringModel> getLogisticsCountryProvinceSelect(TmsLogisticsCountryProvinceListRequest request) {
        List<TmsLogisticsCountryProvinceMappingEntity> logisticsCountryProvinceMappingEntityList = logisticsCountryProvinceMappingRepository.findByLogisticsCompanyAndCountryCode(request.getLogisticsCompany(), request.getCountryCode());
        return logisticsCountryProvinceMappingEntityList.stream().map(entity -> new SelectStringModel(entity.getProvinceCode(), entity.getProvinceName())).collect(Collectors.toList());
    }

    public List<TmsLogisticsCountryProvinceMapping> getLogisticsCountryListByLogisticsCompany(String logisticsCompany) {
        List<TmsLogisticsCountryProvinceMappingEntity> entityList = logisticsCountryProvinceMappingRepository.findByLogisticsCompany(logisticsCompany);
        return entityList.stream().map(entity -> {
            TmsLogisticsCountryProvinceMapping logisticsCountryProvinceMapping = new TmsLogisticsCountryProvinceMapping();
            BeanUtils.copyProperties(entity, logisticsCountryProvinceMapping);
            return logisticsCountryProvinceMapping;
        }).collect(Collectors.toList());

    }
}
