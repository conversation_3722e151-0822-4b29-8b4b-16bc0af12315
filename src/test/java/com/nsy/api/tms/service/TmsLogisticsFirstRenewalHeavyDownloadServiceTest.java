package com.nsy.api.tms.service;

import com.nsy.api.core.apicore.util.JSONUtils;
import com.nsy.api.tms.SpringServiceTest;
import com.nsy.api.tms.dao.entity.TmsLogisticsChannelConfigEntity;
import com.nsy.api.tms.dao.entity.TmsLogisticsFreightCountryMappingEntity;
import com.nsy.api.tms.dao.entity.TmsLogisticsFreightEntity;
import com.nsy.api.tms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.tms.external.user.UserDictionaryApiService;
import com.nsy.api.tms.repository.TmsLogisticsChannelConfigRepository;
import com.nsy.api.tms.repository.TmsLogisticsFreightCountryMappingRepository;
import com.nsy.api.tms.repository.TmsLogisticsFreightRepository;
import com.nsy.api.tms.request.DownloadRequest;
import com.nsy.api.tms.request.TmsLogisticsCompanyListRequest;
import com.nsy.api.tms.response.base.DownloadResponse;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

public class TmsLogisticsFirstRenewalHeavyDownloadServiceTest extends SpringServiceTest {
    @Autowired
    TmsLogisticsFirstRenewalHeavyDownloadService firstRenewalHeavyDownloadService;
    @Autowired
    TmsLogisticsFreightRepository tmsLogisticsFreightRepository;
    @Autowired
    TmsLogisticsChannelConfigRepository tmsLogisticsChannelConfigRepository;
    @Autowired
    TmsLogisticsFreightCountryMappingRepository tmsLogisticsFreightCountryMappingRepository;
    @MockBean
    UserDictionaryApiService userDictionaryApiService;

    @Before
    public void init() {
        Mockito.doReturn(Collections.emptyList())
                .when(userDictionaryApiService).getDictionaryValues(Mockito.anyString());
        TmsLogisticsFreightEntity entity1 = new TmsLogisticsFreightEntity();
        entity1.setLocation("QUANZHOU");
        entity1.setLogisticsCompany("顺丰");
        entity1.setLogisticsChannelCode("SFQD1");
        entity1.setValuationMethod("1");
        entity1.setFreeShippingAmount(new BigDecimal(2));
        entity1.setDefaultUnitPrice(new BigDecimal(5));
        entity1.setDefaultWeightMin(new BigDecimal(3));
        tmsLogisticsFreightRepository.save(entity1);
        TmsLogisticsFreightEntity entity2 = new TmsLogisticsFreightEntity();
        entity2.setLocation("QUANZHOU");
        entity2.setLogisticsCompany("邮政小包");
        entity2.setLogisticsChannelCode("123123");
        entity2.setValuationMethod("FIRST_RENEWAL");
        entity2.setFreeShippingAmount(new BigDecimal(2));
        entity2.setDefaultUnitPrice(new BigDecimal(5));
        entity2.setDefaultWeightMin(new BigDecimal(6));
        tmsLogisticsFreightRepository.save(entity2);
        initChannelfigEntity();
        initFcm();
    }

    public void initFcm() {
        List<TmsLogisticsFreightEntity> list = tmsLogisticsFreightRepository.findAll();
        TmsLogisticsFreightCountryMappingEntity entity1 = new TmsLogisticsFreightCountryMappingEntity();
        entity1.setLocation("QUANZHOU");
        entity1.setLogisticsFreightId(list.get(0).getId());
        entity1.setCountries("中国");
        entity1.setRenewalFee(new BigDecimal(4));
        entity1.setRegistrationFee(new BigDecimal(2));
        entity1.setUnitPrice(new BigDecimal(5));
        entity1.setWeightMin(new BigDecimal(9));
        entity1.setDiscount(new BigDecimal(8));
        entity1.setAging(4);
        entity1.setIntervalFee("1-1:2,4-5:6,5-10:10");
        tmsLogisticsFreightCountryMappingRepository.save(entity1);
        TmsLogisticsFreightCountryMappingEntity entity2 = new TmsLogisticsFreightCountryMappingEntity();
        entity2.setLocation("QUANZHOU");
        entity2.setLogisticsFreightId(list.get(1).getId());
        entity2.setCountries("中国");
        entity2.setRenewalFee(new BigDecimal(11));
        entity2.setRegistrationFee(new BigDecimal(2));
        entity2.setUnitPrice(new BigDecimal(5));
        entity2.setWeightMin(new BigDecimal(13));
        entity2.setDiscount(new BigDecimal(8));
        entity2.setAging(4);
        entity2.setIntervalFee("1-1:2,4-5:6,5-10:10");
        tmsLogisticsFreightCountryMappingRepository.save(entity2);
    }

    public void initChannelfigEntity() {
        TmsLogisticsChannelConfigEntity channelConfig = new TmsLogisticsChannelConfigEntity();
        channelConfig.setLogisticsChannelCode("SFQD1");
        channelConfig.setLogisticsChannelName("顺丰");
        channelConfig.setLogisticsCompany("顺丰");
        channelConfig.setStatus(1);
        tmsLogisticsChannelConfigRepository.save(channelConfig);
        TmsLogisticsChannelConfigEntity channelConfig2 = new TmsLogisticsChannelConfigEntity();
        channelConfig2.setLogisticsChannelCode("123123");
        channelConfig2.setLogisticsChannelName("邮政小包");
        channelConfig2.setLogisticsCompany("邮政小包");
        channelConfig2.setStatus(1);
        tmsLogisticsChannelConfigRepository.save(channelConfig2);
    }

    @After
    public void deleteAll() {
        tmsLogisticsFreightRepository.deleteAll();
        tmsLogisticsChannelConfigRepository.deleteAll();
        tmsLogisticsFreightCountryMappingRepository.deleteAll();
    }

    @Test
    public void typeTest() {
        QuartzDownloadQueueTypeEnum type = firstRenewalHeavyDownloadService.type();
        Assert.assertEquals(type, QuartzDownloadQueueTypeEnum.TMS_LOGISTICS_PRICE_FIRST_RENEWAL_HEAVY);
    }

    @Test
    public void queryExportData() {
        TmsLogisticsCompanyListRequest request = new TmsLogisticsCompanyListRequest();
        request.setPageSize(10);
        request.setPageIndex(1);
        DownloadRequest downloadRequest = new DownloadRequest();
        downloadRequest.setPageSize(10);
        downloadRequest.setPageIndex(1);
        downloadRequest.setType(QuartzDownloadQueueTypeEnum.TMS_LOGISTICS_PRICE_FIRST_RENEWAL_HEAVY);
        downloadRequest.setLocation("QUANZHOU");
        downloadRequest.setRequestContent(JSONUtils.toJSON(request));
        DownloadResponse response = firstRenewalHeavyDownloadService.queryExportData(downloadRequest);
        Assert.assertNotEquals(response.getTotalCount().longValue(), 0L);
    }
}
