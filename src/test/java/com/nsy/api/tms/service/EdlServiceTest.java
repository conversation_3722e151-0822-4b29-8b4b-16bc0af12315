package com.nsy.api.tms.service;

import com.nsy.api.tms.SpringServiceTest;
import com.nsy.api.tms.dao.entity.TmsConfigEntity;
import com.nsy.api.tms.dao.entity.TmsLogisticsChannelConfigEntity;
import com.nsy.api.tms.dao.entity.TmsPackageEntity;
import com.nsy.api.tms.domain.Address;
import com.nsy.api.tms.domain.OrderInfo;
import com.nsy.api.tms.domain.OrderItemInfo;
import com.nsy.api.tms.repository.TmsConfigRepository;
import com.nsy.api.tms.repository.TmsLogisticsChannelConfigRepository;
import com.nsy.api.tms.request.OrderRequest;
import com.nsy.api.tms.response.GenerateOrderResponse;
import com.nsy.api.tms.service.external.HuaLeiService;
import com.nsy.api.tms.utils.Lists;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.List;

@Ignore
public class EdlServiceTest extends SpringServiceTest {

    @Inject
    HuaLeiService huaLeiService;

    @Inject
    PackageService packageService;

    @Inject
    TmsLogisticsChannelConfigRepository tmsLogisticsChannelConfigRepository;

    @Inject
    TmsConfigRepository tmsConfigRepository;

    static String tid;

    @Before
    public void init() {
        // init hualei tid
        tid = "TEST-" + RandomStringUtils.randomAlphanumeric(8);

        // init hualei channel config
        TmsLogisticsChannelConfigEntity tmsLogisticsChannelConfigEntity = getTmsLogisticsChannelConfigEntity();
        tmsLogisticsChannelConfigRepository.save(tmsLogisticsChannelConfigEntity);

        // init hualei authenticate config
        TmsConfigEntity tmsConfigEntity1 = new TmsConfigEntity();
        tmsConfigEntity1.setKeyGroup("EDL");
        tmsConfigEntity1.setKey("edl_userName");
        tmsConfigEntity1.setValue("YCXSY");

        TmsConfigEntity tmsConfigEntity2 = new TmsConfigEntity();
        tmsConfigEntity2.setKeyGroup("EDL");
        tmsConfigEntity2.setKey("edl_password");
        tmsConfigEntity2.setValue("tF3l72WC");
        tmsConfigRepository.saveAll(Lists.newArrayList(tmsConfigEntity1, tmsConfigEntity2));
    }

    @After
    public void clearAllData() {
        tmsLogisticsChannelConfigRepository.deleteAll();
        tmsConfigRepository.deleteAll();
    }

    private TmsLogisticsChannelConfigEntity getTmsLogisticsChannelConfigEntity() {
        TmsLogisticsChannelConfigEntity tmsLogisticsChannelConfigEntity = new TmsLogisticsChannelConfigEntity();
        tmsLogisticsChannelConfigEntity.setLogisticsChannelCode("2741");
        tmsLogisticsChannelConfigEntity.setLogisticsChannelName("1DL华美专线");
        tmsLogisticsChannelConfigEntity.setLogisticsMethod("EDL");
        tmsLogisticsChannelConfigEntity.setKeyGroup("EDL");
        tmsLogisticsChannelConfigEntity.setStatus(1);
        return tmsLogisticsChannelConfigEntity;
    }

    private void setReceiver(OrderInfo orderInfo) {
        orderInfo.setReceiver(new Address());
        orderInfo.getReceiver().setCity("San Fran Cisco");
        orderInfo.getReceiver().setCompany("Marvel");
        orderInfo.getReceiver().setCountry("US");
        orderInfo.getReceiver().setMobile("321");
        orderInfo.getReceiver().setName("Iron Man");
        orderInfo.getReceiver().setPhone("321");
        orderInfo.getReceiver().setPostCode("POST2");
        orderInfo.getReceiver().setProvince("CA");
        orderInfo.getReceiver().setStreet("the star avenue 327");
    }

    private void setSender(OrderInfo orderInfo) {
        orderInfo.setSender(new Address());
        orderInfo.getSender().setCity("成都");
        orderInfo.getSender().setCompany("新时颖");
        orderInfo.getSender().setCountry("CN");
        orderInfo.getSender().setName("发件人");
        orderInfo.getSender().setPhone("123");
        orderInfo.getSender().setPostCode("POST1");
        orderInfo.getSender().setProvince("SC");
        orderInfo.getSender().setStreet("高新区天府三街13层205");
    }

    private OrderRequest getOrderRequest(String tid) {
        OrderRequest request = new OrderRequest();
        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setKeyGroup("EDL");
        orderInfo.setOrderItemInfoList(new ArrayList<>());
        OrderItemInfo orderItemInfo1 = new OrderItemInfo();
        orderItemInfo1.setHsCode("HG001");
        orderItemInfo1.setEnName("big mi");
        orderItemInfo1.setCnName("大米");
        orderItemInfo1.setCount(2);
        orderItemInfo1.setWeight(Double.valueOf(10));
        orderItemInfo1.setCustomsPrice(Double.valueOf(100));
        orderItemInfo1.setUnitPrice(Double.valueOf(110));
        orderInfo.getOrderItemInfoList().add(orderItemInfo1);
        OrderItemInfo orderItemInfo2 = new OrderItemInfo();
        orderItemInfo2.setHsCode("HG002");
        orderItemInfo2.setEnName("small mi");
        orderItemInfo2.setCnName("小米");
        orderItemInfo2.setCount(3);
        orderItemInfo2.setWeight(Double.valueOf(20));
        orderItemInfo2.setCustomsPrice(Double.valueOf(200));
        orderItemInfo2.setUnitPrice(Double.valueOf(210));
        orderInfo.getOrderItemInfoList().add(orderItemInfo2);
        orderInfo.setLogisticsChannelCode("EDL-1DL华美专线");
        orderInfo.setLogisticsMethod("EDL");
        orderInfo.setTid(tid);
        orderInfo.setTmsTid(tid);
        setSender(orderInfo);
        setReceiver(orderInfo);
        request.setOrderInfo(orderInfo);
        return request;
    }

    @Test
    public void generateOrder() {
        System.out.println("hualei generateOrder start");
        OrderRequest request = getOrderRequest(tid);
        TmsLogisticsChannelConfigEntity channelConfigEntity = getTmsLogisticsChannelConfigEntity();
        GenerateOrderResponse response = huaLeiService.syncGenerateOrder(request, channelConfigEntity);
        if (response.getSuccessEntity() != null) {  // 测试创建物流单成功情况
            List<TmsPackageEntity> packageEntityList = packageService.findByTid(tid);
            Assert.assertNotNull(packageEntityList.get(0));
            Assert.assertNotNull(packageEntityList.get(0).getLogisticsNo());
        } else if (response.getError() != null) {  // 测试创建物流单失败情况
            Assert.assertNotNull(response.getError().getMessage());
        }
    }

}