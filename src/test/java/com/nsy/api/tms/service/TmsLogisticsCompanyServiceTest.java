package com.nsy.api.tms.service;

import com.nsy.api.core.apicore.util.JSONUtils;
import com.nsy.api.tms.SpringServiceTest;
import com.nsy.api.tms.dao.entity.TmsLogisticsCompanyEntity;
import com.nsy.api.tms.domain.shared.SelectStringModel;
import com.nsy.api.tms.enumeration.QuartzDownloadQueueTypeEnum;
import com.nsy.api.tms.external.user.UserDictionaryApiService;
import com.nsy.api.tms.repository.TmsLogisticsCompanyRepository;
import com.nsy.api.tms.request.DownloadRequest;
import com.nsy.api.tms.request.StatusRequest;
import com.nsy.api.tms.request.TmsLogisticsCompanyAddRequest;
import com.nsy.api.tms.request.TmsLogisticsCompanyListRequest;
import com.nsy.api.tms.response.base.DownloadResponse;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.Collections;
import java.util.List;
public class TmsLogisticsCompanyServiceTest extends SpringServiceTest {
    @Autowired
    TmsLogisticsCompanyService logisticsCompanyService;
    @Autowired
    TmsLogisticsCompanyRepository logisticsCompanyRepository;
    @MockBean
    UserDictionaryApiService userDictionaryApiService;
    @Before
    public void init() {
        Mockito.doReturn(Collections.emptyList())
                .when(userDictionaryApiService).getDictionaryValues(Mockito.anyString());
        TmsLogisticsCompanyAddRequest request = new TmsLogisticsCompanyAddRequest();
        request.setLocation("QUANZHOU");
        request.setLogisticsCompany("云途");
        request.setLogisticsCode("云途");
        request.setWorkspace("FBA");
        request.setPriority(1);
        //request.setLimitWeight(BigDecimal.valueOf(0.00));
        request.setLabelHeight(0);
        request.setLabelWidth(0);
        request.setIsDefault(1);
        request.setDistributionAvailable(0);
        request.setQueryDisplay(1);
        request.setStatus(1);
        request.setLogisticsType("国际物流");
        request.setLogisticsMethod("FEDEX");
        logisticsCompanyService.addLogisticsCompany(request);
    }
    @After
    public void deleteAll() {
        logisticsCompanyRepository.deleteAll();
    }
    @Test
    public void addLogisticsCompany() {
        List<TmsLogisticsCompanyEntity> logisticsCompanyEntityList = logisticsCompanyRepository.findAll();
        Assert.assertNotNull(logisticsCompanyEntityList);
    }
    @Test
    public void updateLogisticsCompany() {
        List<TmsLogisticsCompanyEntity> logisticsCompanyEntityList = logisticsCompanyRepository.findAll();
        TmsLogisticsCompanyEntity logisticsCompanyEntity = logisticsCompanyEntityList.get(0);
        TmsLogisticsCompanyAddRequest request = new TmsLogisticsCompanyAddRequest();
        BeanUtils.copyProperties(logisticsCompanyEntity, request);
        request.setPriority(2);
        logisticsCompanyService.updateLogisticsCompany(logisticsCompanyEntity.getId(), request);
        TmsLogisticsCompanyEntity entity = logisticsCompanyRepository.findByLogisticsCompany(logisticsCompanyEntity.getLogisticsCompany());
        Assert.assertNotNull(entity);
        Assert.assertTrue(entity.getPriority().equals(2));
    }
    @Test
    public void updateLogisticsCompanyStatus() {
        List<TmsLogisticsCompanyEntity> logisticsCompanyEntityList = logisticsCompanyRepository.findAll();
        TmsLogisticsCompanyEntity logisticsCompanyEntity = logisticsCompanyEntityList.get(0);
        StatusRequest request = new StatusRequest();
        request.setStatus(0);
        logisticsCompanyService.updateLogisticsCompanyStatus(logisticsCompanyEntity.getId(), request);
        TmsLogisticsCompanyEntity entity = logisticsCompanyRepository.findByLogisticsCompany(logisticsCompanyEntity.getLogisticsCompany());
        Assert.assertNotNull(entity);
        Assert.assertTrue(entity.getStatus().equals(0));
    }
    @Test
    public void getAllNameSelect() {
        List<SelectStringModel> selectStringModelList = logisticsCompanyService.getNameSelectByLocation("QUANZHOU");
        Assert.assertNotNull(selectStringModelList);
    }

    @Test
    public void typeTest() {
        QuartzDownloadQueueTypeEnum type = logisticsCompanyService.type();
        Assert.assertEquals(type, QuartzDownloadQueueTypeEnum.TMS_LOGISTICS_COMPANY);
    }

    @Test
    public void queryExportData() {
        TmsLogisticsCompanyListRequest request = new TmsLogisticsCompanyListRequest();
        request.setPageSize(10);
        request.setPageIndex(1);
        DownloadRequest downloadRequest = new DownloadRequest();
        downloadRequest.setPageSize(10);
        downloadRequest.setPageIndex(1);
        downloadRequest.setType(QuartzDownloadQueueTypeEnum.TMS_LOGISTICS_COMPANY);
        downloadRequest.setLocation("QUANZHOU");
        downloadRequest.setRequestContent(JSONUtils.toJSON(request));
        DownloadResponse response = logisticsCompanyService.queryExportData(downloadRequest);
        Assert.assertNotEquals(response.getTotalCount().longValue(), 0L);
    }
}
